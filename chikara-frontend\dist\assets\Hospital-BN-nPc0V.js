import{r as b,j as e,V as J,W as ee,Y as D,_ as te,$ as B,a0 as q,a1 as se,e as G,g as U,c as E,l as S,h as _,a2 as ae,a3 as re,y as ne,a4 as le,a5 as T,B as V,b as z,a6 as ie,t as ce,v as oe,a7 as M,D as de,o as ue}from"./index-DjvF_jFD.js";import{D as me}from"./DataTable-BPndfWaa.js";import{u as xe}from"./index-_htw0zgA.js";import"./ag-theme-quartz-B5A8h4W5.js";var L="Checkbox",[fe,we]=J(L),[he,A]=fe(L);function pe(t){const{__scopeCheckbox:u,checked:i,children:j,defaultChecked:f,disabled:a,form:h,name:p,onCheckedChange:c,required:g,value:d="on",internal_do_not_use_render:y}=t,[v,C]=te({prop:i,defaultProp:f??!1,onChange:c,caller:L}),[m,s]=b.useState(null),[k,o]=b.useState(null),n=b.useRef(!1),N=m?!!h||!!m.closest("form"):!0,I={checked:v,disabled:a,setChecked:C,control:m,setControl:s,name:p,form:h,value:d,hasConsumerStoppedPropagationRef:n,required:g,defaultChecked:w(f)?!1:f,isFormControl:N,bubbleInput:k,setBubbleInput:o};return e.jsx(he,{scope:u,...I,children:be(y)?y(I):j})}var K="CheckboxTrigger",Q=b.forwardRef(({__scopeCheckbox:t,onKeyDown:u,onClick:i,...j},f)=>{const{control:a,value:h,disabled:p,checked:c,required:g,setControl:d,setChecked:y,hasConsumerStoppedPropagationRef:v,isFormControl:C,bubbleInput:m}=A(K,t),s=B(f,d),k=b.useRef(c);return b.useEffect(()=>{const o=a?.form;if(o){const n=()=>y(k.current);return o.addEventListener("reset",n),()=>o.removeEventListener("reset",n)}},[a,y]),e.jsx(D.button,{type:"button",role:"checkbox","aria-checked":w(c)?"mixed":c,"aria-required":g,"data-state":Z(c),"data-disabled":p?"":void 0,disabled:p,value:h,...j,ref:s,onKeyDown:q(u,o=>{o.key==="Enter"&&o.preventDefault()}),onClick:q(i,o=>{y(n=>w(n)?!0:!n),m&&C&&(v.current=o.isPropagationStopped(),v.current||o.stopPropagation())})})});Q.displayName=K;var F=b.forwardRef((t,u)=>{const{__scopeCheckbox:i,name:j,checked:f,defaultChecked:a,required:h,disabled:p,value:c,onCheckedChange:g,form:d,...y}=t;return e.jsx(pe,{__scopeCheckbox:i,checked:f,defaultChecked:a,disabled:p,required:h,onCheckedChange:g,name:j,form:d,value:c,internal_do_not_use_render:({isFormControl:v})=>e.jsxs(e.Fragment,{children:[e.jsx(Q,{...y,ref:u,__scopeCheckbox:i}),v&&e.jsx(X,{__scopeCheckbox:i})]})})});F.displayName=L;var W="CheckboxIndicator",Y=b.forwardRef((t,u)=>{const{__scopeCheckbox:i,forceMount:j,...f}=t,a=A(W,i);return e.jsx(ee,{present:j||w(a.checked)||a.checked===!0,children:e.jsx(D.span,{"data-state":Z(a.checked),"data-disabled":a.disabled?"":void 0,...f,ref:u,style:{pointerEvents:"none",...t.style}})})});Y.displayName=W;var $="CheckboxBubbleInput",X=b.forwardRef(({__scopeCheckbox:t,...u},i)=>{const{control:j,hasConsumerStoppedPropagationRef:f,checked:a,defaultChecked:h,required:p,disabled:c,name:g,value:d,form:y,bubbleInput:v,setBubbleInput:C}=A($,t),m=B(i,C),s=xe(a),k=se(j);b.useEffect(()=>{const n=v;if(!n)return;const N=window.HTMLInputElement.prototype,R=Object.getOwnPropertyDescriptor(N,"checked").set,P=!f.current;if(s!==a&&R){const r=new Event("click",{bubbles:P});n.indeterminate=w(a),R.call(n,w(a)?!1:a),n.dispatchEvent(r)}},[v,s,a,f]);const o=b.useRef(w(a)?!1:a);return e.jsx(D.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??o.current,required:p,disabled:c,name:g,value:d,form:y,...u,tabIndex:-1,ref:m,style:{...u.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});X.displayName=$;function be(t){return typeof t=="function"}function w(t){return t==="indeterminate"}function Z(t){return w(t)?"indeterminate":t?"checked":"unchecked"}const ge=()=>{const t=G();return{healUser:U(E.infirmary.revivePlayer.mutationOptions({onSuccess:()=>{S.success("You healed the user!"),t.invalidateQueries({queryKey:E.infirmary.getInjuredList.key()})},onError:i=>{S.error(i?.message||"An error occurred")}}))}},O=b.forwardRef(({className:t,...u},i)=>e.jsx(F,{ref:i,className:_("peer size-4 shrink-0 rounded-xs border border-zinc-900 bg-gray-900 shadow-sm focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:cursor-not-allowed disabled:opacity-25 data-[state=checked]:bg-gray-900 data-[state=checked]:text-zinc-50 dark:border-zinc-700 dark:data-[state=checked]:bg-zinc-800 dark:data-[state=checked]:text-gray-900 dark:focus-visible:ring-zinc-300",t),...u,children:e.jsx(Y,{className:_("flex items-center justify-center text-current"),children:e.jsx(ae,{className:"size-4 text-white"})})}));O.displayName=F.displayName;const je=()=>{const t=G();return{hospitalCheckIn:U(E.infirmary.hospitalCheckIn.mutationOptions({onSuccess:()=>{S.success("Checked in successfully!"),t.invalidateQueries({queryKey:E.infirmary.getHospitalList.key()}),t.invalidateQueries({queryKey:E.user.getCurrentUserInfo.key()})},onError:i=>{S.error(i?.message||"An error occurred")}}))}},ye=({currentUser:t})=>{const{data:u}=re(),{hospitalCheckIn:i}=je(),{MINOR_COST_PER_LEVEL:j,MODERATE_COST_PER_LEVEL:f,SEVERE_COST_PER_LEVEL:a,COST_PER_HP:h}=ne(),[p,c]=b.useState(!1),g=t?.health-t?.currentHealth,d=u.filter(s=>s.effect&&s.effect.effectType==="DEBUFF"),y=()=>{if(!d||!t)return 0;const s=t.health-t.currentHealth,k=t.level,o=d.filter(l=>l.effect.tier==="Minor"),n=d.filter(l=>l.effect.tier==="Moderate"),N=d.filter(l=>l.effect.tier==="Severe"),I=o?.length*(k*j)||0,R=n?.length*(k*f)||0,P=N?.length*(k*a)||0,r=s*h||0;let x=I+R+P;return p||(x+=r),Math.round(x)},v=(s,k)=>{if(!d||!t)return 0;const o=t.level;let n=0;switch(s){case"Minor":n=o*j;break;case"Moderate":n=o*f;break;case"Severe":n=o*a;break;case"missingHP":n=k*h;break;default:n=0;break}return Math.round(n)},C=s=>{switch(s){case"Minor":return"text-red-400";case"Moderate":return"text-red-500";case"Severe":return"text-red-600";case"missingHP":return"text-red-400";default:return"text-red-400"}},m=y();return e.jsxs("div",{className:"mx-auto my-4 flex w-[90%] flex-col justify-between rounded-lg border border-gray-600 bg-slate-800 px-4 py-2 md:w-fit md:min-w-96 md:px-6",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsx("p",{className:"mb-0.5 text-blue-300 text-sm uppercase",children:"Your Injuries"}),!t?.hospitalisedUntil&&e.jsxs("div",{className:"flex items-center space-x-2 md:my-auto md:hidden dark:text-gray-200",children:[e.jsx(O,{disabled:!m||d.length<1,checked:p,onCheckedChange:s=>c(s)}),e.jsx("label",{className:"font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Heal Injuries Only"})]})]}),d?.length===0&&g===0&&e.jsx("li",{className:"text-gray-300 text-sm",children:"None"}),d?.map(s=>e.jsx("div",{children:s.effect.name!=="Zombified"&&e.jsxs("li",{className:"flex flex-row gap-1.5 text-gray-200",children:[s.stacks,"x"," ",e.jsx("img",{src:le[s?.effect?.source]?.icon,alt:"",className:"mt-0.5 h-5 w-auto rounded-md border border-red-500"}),e.jsx("p",{className:C(s.effect.tier),children:s.effect.name}),e.jsxs("div",{className:"ml-2 flex flex-row items-center gap-1 text-yellow-500",children:[e.jsx("img",{className:"mb-0.5 h-4 w-auto",src:T,alt:""}),e.jsx("p",{className:"text-yellow-500",children:v(s.effect.tier)})]})]})},s.id)),g>0&&e.jsxs(e.Fragment,{children:[e.jsx("hr",{className:"my-2 border-gray-600/75"}),e.jsx("div",{children:e.jsxs("li",{className:_(p&&"line-through","flex flex-row gap-1.5 text-gray-200"),children:[g,e.jsx("p",{className:C("missingHP"),children:"Health Points"}),e.jsxs("div",{className:"ml-2 flex flex-row items-center gap-1 text-yellow-500",children:[e.jsx("img",{className:"mb-0.5 h-4 w-auto",src:T,alt:""}),e.jsx("p",{className:"text-yellow-500",children:v("missingHP",g)})]})]})})]})]}),e.jsx("hr",{className:"my-2 border-gray-600/75"}),t?.hospitalisedUntil?e.jsx("div",{className:"flex flex-col items-center",children:e.jsx("p",{className:"text-green-500 text-lg",children:"Receiving Treatment"})}):e.jsxs("div",{className:"flex flex-row items-end gap-6",children:[m>0?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Length"}),e.jsx("div",{className:"flex flex-row items-center gap-1.5 text-blue-500 text-lg",children:e.jsx("p",{children:"10 Minutes"})})]}),e.jsxs("div",{className:"ml-auto flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Total Cost"}),e.jsxs("div",{className:"flex flex-row items-center gap-1.5 text-xl text-yellow-500",children:[e.jsx("img",{className:"h-6 w-auto",src:T,alt:""}),e.jsx("p",{children:m})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Length"}),e.jsx("div",{className:"text-center text-xl",children:"-"})]}),e.jsxs("div",{className:"ml-auto flex flex-col",children:[e.jsx("p",{className:"text-gray-300 text-xs uppercase",children:"Total Cost"}),e.jsx("div",{className:"text-center text-xl",children:"-"})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row md:gap-6",children:[e.jsxs("div",{className:"hidden items-center space-x-2 md:my-auto md:flex dark:text-gray-200",children:[e.jsx(O,{checked:p,disabled:!m||d.length<1,onCheckedChange:s=>c(s)}),e.jsx("label",{className:"font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Heal Injuries Only"})]}),e.jsx(V,{className:"w-24! ml-auto!",disabled:!m,onClick:()=>i.mutate(p),children:"Check In"})]})]})]})};function Ie(){const{isLoading:t,error:u,data:i}=z(E.infirmary.getHospitalList.queryOptions({select:r=>r.sort((x,l)=>x.hospitalisedUntil>l.hospitalisedUntil?1:-1)})),{data:j,isLoading:f}=z(E.infirmary.getInjuredList.queryOptions({select:r=>r.sort((x,l)=>l.endsAt>x.endsAt?1:-1)})),a=ie("revive"),[h,p]=b.useState("injured"),c=h==="injured",g=0,d=a?.modifier-g||0,{healUser:y}=ge(),v=(r,x,l,H)=>l.data.id-H.data.id,{data:C}=ce(),m=oe(),s=r=>r?r.id===1?"text-green-500":"text-red-400":"text-gray-400",k=r=>{const x=r?.data?.user_status_effect;return x&&x?.length<1?e.jsx("div",{className:"!flex items-center! justify-center! w-full! font-semibold",children:e.jsx("p",{className:"m-auto! text-red-400 text-xs md:text-sm",children:"Regaining Health"})}):e.jsxs("div",{className:"h-full! flex! text-center! justify-center! flex-col items-center gap-1.5 py-1.5 font-semibold",children:[x?.map(l=>e.jsx("div",{children:e.jsxs("li",{className:"flex flex-row gap-1.5 text-gray-200 text-xs leading-none md:text-sm",children:[l?.stacks,"x"," ",e.jsx("p",{className:"text-red-500",children:l?.customName?l?.customName:l?.effect?.name})]})},l?.id)),c&&e.jsxs("p",{className:"text-[0.6rem] text-blue-400 leading-none md:text-sm",children:["Fully healed in ",M(r?.data?.endsAt)]})]})},o=r=>{const x=r?.data?.user_status_effect?.[0]?.id;return e.jsx("div",{className:"flex size-full",children:e.jsxs(V,{className:"m-auto!",disabled:d===0,onClick:()=>y.mutate(x),children:["Heal (",d,")"]})})},n=r=>{const{username:x,id:l,gang:H}=r?.data;return e.jsxs("div",{className:_(r.data?.defeated&&"grayscale",r.data?.disabled&&"opacity-25 grayscale","relative flex h-full items-center justify-normal px-0.5 py-0 font-lili md:w-full md:gap-2"),children:[e.jsxs("div",{className:"mt-1.5 flex min-w-13 flex-col items-center justify-center gap-1 text-center md:mt-0",children:[e.jsx(de,{src:r?.data,className:"aspect-square! size-11 rounded-full border border-blue-800 md:size-13"}),l&&e.jsxs("small",{className:"block font-semibold text-gray-500 text-xs md:hidden dark:text-blue-400",children:["#",l]})]}),e.jsx(ue,{to:x?`/profile/${l}`:null,children:e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:_("font-bold text-sm text-stroke-sm md:text-base",x?"text-blue-600":"text-gray-200"),children:[x||"Anonymous"," "]}),l&&e.jsxs("div",{className:"hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-400",children:["ID ",e.jsxs("span",{className:"text-indigo-400",children:["#",l]})]}),e.jsx("div",{className:_(s(H),"mt-1 block font-semibold text-xs md:hidden md:text-sm dark:text-stroke-sm"),children:H===null?"No Gang":H?.name})]})})]})};b.useLayoutEffect(()=>{R(N)},[h,g]);const N=[{headerName:"Name",field:"id",comparator:v,cellRenderer:n,minWidth:m?160:300,maxWidth:m?160:null,suppressFloatingFilterButton:!0,filterParams:{filterOptions:["contains"],defaultOption:"contains"}},{headerName:"Injuries",field:"hospitalisedReason",minWidth:m?120:null,maxWidth:m?120:null,cellRenderer:k,cellClass:"items-center! justify-center! flex!",headerClass:"centerGridHeader",filter:!1,autoHeight:!0},{headerName:c?"Actions":"Duration",field:c?"endsAt":"hospitalisedUntil",headerClass:"centerGridHeader",cellClass:"md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",cellRenderer:c?o:null,filter:!1,floatingFilter:!1,valueFormatter:c?null:r=>M(r.value),filterValueGetter:c?null:r=>M(r.value)}],[I,R]=b.useState(N);if(u)return"An error has occurred: "+u.message;const P=[{name:"Injured List",value:"injured",current:h==="injured"},{name:"Patients List",value:"treatment",current:h==="treatment"}];return e.jsxs("div",{className:"mb-8 md:mx-auto md:mb-0 md:max-w-6xl",children:[e.jsx(ye,{currentUser:C}),e.jsx(me,{dataList:c?j:i,colDefs:I,isLoading:t||f,setCurrentTab:p,currentTab:h,tabs:P})]})}export{Ie as default};
