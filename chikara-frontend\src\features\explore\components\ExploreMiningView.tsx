import { useQueryClient } from "@tanstack/react-query";
import { motion, useAnimation, type Variants } from "framer-motion";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import missingIcon from "@/assets/images/missingIcon.png";
import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import type { ExploreNodeLocation } from "../types/explore.types";
import StatusEffects from "@/components/StatusEffects";
import { rarityColours } from "@/helpers/rarityColours";
import { sceneManager } from "@/helpers/sceneManager";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import type { Item } from "@/types/item";
import useExploreMiningOperation from "../api/useExploreMiningOperation";

interface MiningViewProps {
    nodeId: number;
    location: ExploreNodeLocation;
    miningType: string;
    difficulty: "easy" | "medium" | "hard";
    onClose: () => void;
}

interface MiningResult {
    success: boolean;
    message: string;
    itemReward?: Item;
    itemQuantity?: number;
    injury?: {
        name: string;
        description: string;
    };
    jailed?: boolean;
    jailDuration?: number;
}

// Constants
const ANIMATION_VARIANTS = {
    container: {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 1 } },
    } as Variants,
    wipe: {
        hidden: { width: "100%", left: 0 },
        visible: {
            width: 0,
            left: "50%",
            transition: { ease: "easeInOut", duration: 0.75 },
        },
    } as Variants,
    dialogueBox: {
        hidden: { opacity: 0, scale: 0.9 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: { duration: 0.3, ease: "easeOut" },
        },
    } as Variants,
};

const MINING_ICONS: Record<string, string> = {
    coal: "https://img.icons8.com/?size=100&id=1437&format=png",
    copper: "https://img.icons8.com/?size=100&id=qf6FQ7o8i8DI&format=png",
    iron: "https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png",
    gold: "https://img.icons8.com/?size=100&id=43138&format=png",
    crystal: "https://img.icons8.com/?size=100&id=fDyZg5u1hAeF&format=png",
    gems: "https://img.icons8.com/?size=100&id=11234&format=png",
    ore: "https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png",
};

const DIFFICULTY_CONFIG = {
    easy: {
        color: "text-green-400",
        bgColor: "bg-green-500/20 border-green-400/30",
        icon: "⚡",
        label: "Easy",
        description: "Low risk, steady rewards",
    },
    medium: {
        color: "text-yellow-400",
        bgColor: "bg-yellow-500/20 border-yellow-400/30",
        icon: "⚠️",
        label: "Medium",
        description: "Moderate risk, better rewards",
    },
    hard: {
        color: "text-red-400",
        bgColor: "bg-red-500/20 border-red-400/30",
        icon: "💀",
        label: "Hard",
        description: "High risk, valuable rewards",
    },
};

// Utility functions
const getMiningIcon = (miningType: string): string => MINING_ICONS[miningType] || MINING_ICONS.ore || missingIcon;

// Custom hooks
const useMiningQueries = () => {
    const queryClient = useQueryClient();

    const invalidateQueries = async () => {
        await Promise.all([
            queryClient.invalidateQueries({
                queryKey: api.explore.getMapByLocation.key(),
            }),
            queryClient.invalidateQueries({
                queryKey: api.user.getCurrentUserInfo.key(),
            }),
        ]);
    };

    return { invalidateQueries };
};

// Status Icon Component
const StatusIcon = ({ isSuccess }: { isSuccess: boolean }) => {
    const iconClass = isSuccess ? "text-green-400" : "text-red-400";
    const bgClass = isSuccess ? "bg-green-500/20" : "bg-red-500/20";

    return (
        <div className={cn("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center", bgClass)}>
            <svg
                className={cn("w-8 h-8 md:w-10 md:h-10", iconClass)}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                {isSuccess ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                )}
            </svg>
        </div>
    );
};

// Item Reward Component
const ItemReward = ({ item, quantity }: { item: Item; quantity?: number }) => (
    <div className="bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30">
        <div className="flex items-center justify-center gap-3 md:gap-4">
            <span className="text-white text-lg md:text-xl font-semibold">{quantity ?? 1}x</span>
            <DisplayItem item={item} height="h-10 w-auto md:h-12 lg:h-14" className="inline-block" />
            <span className={cn("text-lg md:text-xl font-semibold", rarityColours(item.rarity))}>{item.name}</span>
        </div>
    </div>
);

// Mining Action Button Component
const MiningActionButton = ({
    miningType,
    difficulty,
    onStartMining,
    isDisabled,
}: {
    miningType: string;
    difficulty: "easy" | "medium" | "hard";
    onStartMining: () => void;
    isDisabled: boolean;
}) => {
    const difficultyConfig = DIFFICULTY_CONFIG[difficulty];

    return (
        <button
            type="button"
            disabled={isDisabled}
            className={cn(
                "cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-amber-600 to-orange-700",
                "hover:from-amber-500 hover:to-orange-600 transition-all duration-300",
                "border-2 border-amber-400/30 hover:border-amber-400/60",
                "shadow-lg hover:shadow-xl transform hover:scale-[1.02]",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",
                "p-6 md:p-8 w-full max-w-md mx-auto"
            )}
            onClick={onStartMining}
        >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-4">
                <div className="flex items-center gap-4">
                    <img
                        src={getMiningIcon(miningType)}
                        className="w-12 h-12 md:w-16 md:h-16 object-contain"
                        alt={`${miningType} mining`}
                    />
                    <div className="text-left">
                        <h3 className="text-white font-bold text-lg md:text-xl capitalize">Mine {miningType}</h3>
                        <div className={cn("text-sm font-medium", difficultyConfig.color)}>
                            {difficultyConfig.icon} {difficultyConfig.label}
                        </div>
                    </div>
                </div>
                <p className="text-gray-200 text-sm text-center">{difficultyConfig.description}</p>
            </div>
        </button>
    );
};

// Result Panel Component
const ResultPanel = ({
    miningResult,
    onAction,
    isLoading,
    isMobile,
    actionButtonText,
    actionButtonVariant = "primary",
}: {
    miningResult: MiningResult;
    onAction: () => void;
    isLoading: boolean;
    isMobile: number | boolean;
    actionButtonText: string;
    actionButtonVariant?: "primary" | "destructive";
}) => (
    <div className="flex flex-col items-center gap-6 md:gap-8 text-center">
        <div className="space-y-4">
            <StatusIcon isSuccess={miningResult.success} />
            <p className="text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl">
                {miningResult.message}
            </p>
        </div>

        {/* Item Reward for Success */}
        {miningResult.success && miningResult.itemReward && (
            <ItemReward item={miningResult.itemReward} quantity={miningResult.itemQuantity} />
        )}

        {/* Status Effects for Failure */}
        {!miningResult.success && miningResult.injury && (
            <div className="bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30">
                <StatusEffects
                    currentEffects={[{ id: 1, count: 1, debuff: miningResult.injury }]}
                    className="justify-center"
                />
            </div>
        )}

        <div className="w-full max-w-sm">
            <Button
                variant={actionButtonVariant}
                size={isMobile ? "md" : "lg"}
                isLoading={isLoading}
                className="w-full text-base md:text-lg"
                onClick={onAction}
            >
                {actionButtonText}
            </Button>
        </div>
    </div>
);

export const MiningView = ({ nodeId, miningType, difficulty, onClose }: MiningViewProps) => {
    const controls = useAnimation();
    const [isLoading, setIsLoading] = useState(false);
    const [miningResult, setMiningResult] = useState<MiningResult | null>(null);
    const isMobile = useCheckMobileScreen();
    const navigate = useNavigate();
    const { invalidateQueries } = useMiningQueries();

    const { mutate: processMiningOperation, isPending: isProcessingMining } = useExploreMiningOperation();
    const sceneImage = sceneManager("citystreet1"); // You might want to add mining-specific scenes

    useEffect(() => {
        controls.start("visible");
    }, [controls]);

    const handleStartMining = () => {
        if (isProcessingMining) return;

        processMiningOperation(
            { nodeId },
            {
                onSuccess: (response) => {
                    setMiningResult({
                        success: response.success || false,
                        message: response.message || "Mining operation completed",
                        itemReward: response.itemReward as Item,
                        itemQuantity: response.itemQuantity,
                        injury: response.injury,
                    });
                },
                onError: (error) => {
                    setMiningResult({
                        success: false,
                        message: error.message || "Something went wrong during mining",
                    });
                },
            }
        );
    };

    const handleClose = async () => {
        setIsLoading(true);
        await invalidateQueries();
        onClose();
        setIsLoading(false);
    };

    const handleJailNavigation = async () => {
        setIsLoading(true);
        await invalidateQueries();
        navigate("/jail");
    };

    const getActionButton = () => {
        if (!miningResult) return null;

        if (miningResult.jailed) {
            return (
                <ResultPanel
                    miningResult={miningResult}
                    isLoading={isLoading}
                    isMobile={isMobile}
                    actionButtonText="Go to Jail"
                    actionButtonVariant="destructive"
                    onAction={handleJailNavigation}
                />
            );
        }

        return (
            <ResultPanel
                miningResult={miningResult}
                isLoading={isLoading}
                isMobile={isMobile}
                actionButtonText="Return to Explore"
                onAction={handleClose}
            />
        );
    };

    const difficultyConfig = DIFFICULTY_CONFIG[difficulty];

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
            <motion.div
                className="relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl"
                variants={ANIMATION_VARIANTS.container}
                initial="hidden"
                animate={controls}
            >
                {/* Background Image */}
                <div className="absolute inset-0">
                    <img className="w-full h-full object-cover" src={sceneImage || ""} alt="Mining location" />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50" />
                </div>

                {/* Animated Wipe Effect */}
                <motion.div
                    className="absolute inset-0 bg-black z-10"
                    variants={ANIMATION_VARIANTS.wipe}
                    initial="hidden"
                    animate="visible"
                />

                {/* Content Container */}
                <motion.div
                    variants={ANIMATION_VARIANTS.dialogueBox}
                    initial="hidden"
                    animate="visible"
                    className="relative z-20 h-full flex items-center justify-center p-4"
                >
                    <div className="w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-amber-500/80 rounded-2xl shadow-2xl">
                        {/* Header */}
                        <div className="px-6 py-4 border-b border-amber-500/30">
                            <h2 className="text-center text-amber-400 text-xl md:text-2xl lg:text-3xl font-semibold">
                                Mining Operation
                            </h2>
                            <div
                                className={cn(
                                    "text-center mt-2 px-3 py-1 rounded-full inline-block",
                                    difficultyConfig.bgColor
                                )}
                            >
                                <span className={cn("text-sm font-medium", difficultyConfig.color)}>
                                    {difficultyConfig.icon} {difficultyConfig.label} Difficulty
                                </span>
                            </div>
                        </div>

                        {/* Main Content */}
                        <div className="p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col">
                            {miningResult ? (
                                <div className="flex-1 flex flex-col justify-center">{getActionButton()}</div>
                            ) : (
                                <div className="flex-1 flex flex-col justify-center gap-6 md:gap-8">
                                    <div className="text-center">
                                        <p className="text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto">
                                            You&apos;ve discovered a rich mining site with{" "}
                                            <span className="text-amber-400 font-semibold capitalize">
                                                {miningType}
                                            </span>{" "}
                                            deposits. The mining operation will consume energy and carries risks, but
                                            may yield valuable materials.
                                        </p>
                                    </div>

                                    {isProcessingMining ? (
                                        <div className="text-center">
                                            <div className="inline-flex items-center gap-2 text-gray-300 text-base md:text-lg">
                                                <div className="size-6 border-2 border-amber-400 border-t-transparent rounded-full animate-spin" />
                                                <span>Mining in progress...</span>
                                            </div>
                                        </div>
                                    ) : (
                                        <MiningActionButton
                                            miningType={miningType}
                                            difficulty={difficulty}
                                            isDisabled={isProcessingMining}
                                            onStartMining={handleStartMining}
                                        />
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </motion.div>
            </motion.div>
        </div>
    );
};
