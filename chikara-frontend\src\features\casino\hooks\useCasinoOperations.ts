import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";

/**
 * Hook for slot machine operations
 */
export const useSlotMachine = () => {
    const queryClient = useQueryClient();

    const gambleMutation = useMutation(
        api.casino.gamble.mutationOptions({
            onSuccess: () => {
                // Invalidate user info to update cash balance
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "Gambling failed";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );

    const gamble = (amount: number) => {
        gambleMutation.mutate({ amount });
    };

    return {
        gamble,
        isGambling: gambleMutation.isPending,
        gambleError: gambleMutation.error,
    };
};

/**
 * Hook for lottery operations
 */
export const useLottery = () => {
    const queryClient = useQueryClient();

    // Get active lottery
    const { data: activeLottery, isLoading: isLoadingLottery } = useQuery(
        api.casino.getLottery.queryOptions({
            staleTime: 30000, // 30 seconds
        })
    );

    // Check lottery entry status
    const { data: hasEntry, isLoading: isLoadingEntry } = useQuery(
        api.casino.checkLotteryEntry.queryOptions({
            input: { id: activeLottery?.id?.toString() || "" },
            enabled: !!activeLottery?.id,
            staleTime: 15000, // 15 seconds
        })
    );

    // Enter lottery mutation
    const enterLotteryMutation = useMutation(
        api.casino.enterLottery.mutationOptions({
            onSuccess: () => {
                // Invalidate related queries
                queryClient.invalidateQueries({
                    queryKey: api.casino.getLottery.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.casino.checkLotteryEntry.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                toast.success("Successfully entered lottery!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Failed to enter lottery";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );

    const enterLottery = (lotteryId: number) => {
        enterLotteryMutation.mutate({ lotteryId });
    };

    return {
        activeLottery,
        hasEntry,
        isLoading: isLoadingLottery || isLoadingEntry,
        enterLottery,
        isEntering: enterLotteryMutation.isPending,
        enterError: enterLotteryMutation.error,
    };
};

/**
 * Hook for roulette operations
 */
export const useRoulette = () => {
    const queryClient = useQueryClient();

    const placeBetMutation = useMutation(
        api.casino.placeBet.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "Failed to place bet";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );

    const placeBet = (bets: Record<string, number>) => {
        placeBetMutation.mutate(bets);
    };

    return {
        placeBet,
        isPlacingBet: placeBetMutation.isPending,
        placeBetError: placeBetMutation.error,
    };
};

/**
 * Combined casino operations hook
 */
export const useCasinoOperations = () => {
    const slotMachine = useSlotMachine();
    const lottery = useLottery();
    const roulette = useRoulette();

    return {
        slotMachine,
        lottery,
        roulette,
    };
};

export default useCasinoOperations;
