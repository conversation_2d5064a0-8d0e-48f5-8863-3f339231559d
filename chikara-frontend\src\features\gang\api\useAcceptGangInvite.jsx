import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useAcceptGangInvite = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.acceptInvite.mutationOptions({
            onSuccess: () => {
                toast.success(`Gang joined!`);
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.gang.getCurrentInvites.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        acceptInvite: mutation.mutate,
    };
};

export default useAcceptGangInvite;
