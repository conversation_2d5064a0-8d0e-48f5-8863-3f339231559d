import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Request parameters for collecting a completed craft
 */
export interface CollectCraftParams {
    id: number;
}

/**
 * Custom hook to collect/complete a crafted item using ORPC
 * @param onSuccessCallback - Optional callback function to execute on successful collection
 */
const useCollectCraft = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.crafting.completeCraft.mutationOptions({
            onSuccess: () => {
                toast.success("Item collected successfully!");

                // Invalidate relevant queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.crafting.getCraftingQueue.key(),
                });

                queryClient.invalidateQueries({
                    queryKey: api.user.getInventory.key(),
                });

                // Execute additional callback if provided
                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error("Collect craft error:", errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};

export default useCollectCraft;
