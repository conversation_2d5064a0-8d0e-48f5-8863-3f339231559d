import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

/**
 * Hook to get all suggestions
 */
export const useGetSuggestions = (options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.getSuggestions.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get user's vote history
 */
export const useGetVoteHistory = (options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.getVoteHistory.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get comments for a suggestion
 */
export const useGetSuggestionComments = (suggestionId: number, options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.getComments.queryOptions({
            input: { id: suggestionId },
            ...options,
        })
    );
};
