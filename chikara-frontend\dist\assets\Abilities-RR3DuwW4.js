import{j as t,bL as A,bM as x,h as o,bK as q,bP as M,r as g,e as C,g as h,c,am as j}from"./index-DjvF_jFD.js";import{t as b}from"./talentData-CZzZ7076.js";function E({abilityInfo:e,setAbilityInfo:r,setAbilitySelected:a,unequipAbility:n,abilitySelected:d}){return t.jsx(A,{children:t.jsxs(x.div,{initial:{y:"100%"},animate:{y:0},transition:{ease:"easeIn",duration:.01},className:o("dark talentInfoModalAnim fixed inset-x-0 bottom-0 z-300 min-h-[20%] w-full transform-none overflow-y-auto border-2 border-yellow-600 bg-gray-800 px-4 pt-1 pb-4 text-stroke-sm transition-transform md:bottom-5 md:left-[31.5%] md:w-1/3"),children:[t.jsxs("h5",{className:o("mt-0.5 mb-1 inline-flex items-center text-2xl uppercase",e?.talentType==="locked"?"text-gray-500":"text-indigo-600 dark:text-indigo-500"),children:[t.jsx(x.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:t.jsx("img",{alt:"",src:e?.image,className:o("mr-2.5 h-14 rounded-md text-gray-500 dark:text-gray-200",e?.talentType==="locked"?"w-10 brightness-90":"w-14")})},e?.image),t.jsxs("div",{children:[e?.talentType==="ability"&&t.jsx("p",{className:"text-orange-400 text-stroke-sm text-xs",children:"Ability"}),e?.talentType==="passive"&&t.jsx("p",{className:"text-sky-400 text-stroke-sm text-xs",children:"Passive"}),e?.displayName?e?.displayName:e?.name]})]}),t.jsxs("button",{type:"button",className:"absolute top-2.5 right-2.5 inline-flex size-8 items-center justify-center rounded-lg bg-gray-600 text-slate-200 text-sm hover:bg-gray-200 hover:text-gray-900 md:bg-transparent dark:hover:bg-gray-600 dark:hover:text-white",onClick:()=>{r(null),a(null)},children:[t.jsx("svg",{className:"size-3","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"})}),t.jsx("span",{className:"sr-only",children:"Close menu"})]}),t.jsx("p",{className:"max-w-lg text-gray-500 text-sm dark:text-gray-400",children:typeof e?.description=="function"?e.description({tier1Modifier:e.tier1Modifier||0,tier2Modifier:e.tier2Modifier||0,tier3Modifier:e.tier3Modifier||0,secondaryModifier:e.secondaryModifier||0}):e?.description}),e?.subDescription&&t.jsx("p",{className:"max-w-lg text-gray-500 text-sm dark:text-gray-400",children:typeof e?.subDescription=="function"?e.subDescription({tier1Modifier:e.tier1Modifier||0,tier2Modifier:e.tier2Modifier||0,tier3Modifier:e.tier3Modifier||0,secondaryModifier:e.secondaryModifier||0}):e?.subDescription}),e?.staminaCost&&e.staminaCost>0&&t.jsxs("p",{className:"max-w-lg text-orange-400 text-sm dark:text-orange-400",children:[e.staminaCost," STA"]}),e&&!d&&t.jsx("div",{className:"mt-6 flex w-full flex-col",children:t.jsx("button",{className:o("mx-auto rounded-lg border border-gray-900 px-4 py-2 text-center font-medium text-base text-stroke-sm focus:outline-hidden focus:ring-4","bg-red-700 text-white hover:bg-red-800 focus:ring-red-300 dark:bg-red-600 dark:focus:ring-red-800 dark:hover:bg-red-700"),onClick:()=>n?.mutate?.(e?.id),children:"Unequip"})})]})})}function z(){const{data:e}=q({select:s=>({talentList:s?.talentList?.filter(i=>i.talentInfo.staminaCost>0),treePoints:s?.treePoints})}),{data:r}=M(),[a,n]=g.useState(null),[d,l]=g.useState(null),u=C(),p=r?[r[0]?.id,r[1]?.id,r[2]?.id,r[3]?.id]:[],k=r?[r[0],r[1],r[2],r[3]]:[],f=h(c.talents.equipAbility.mutationOptions({onSuccess:()=>{u.invalidateQueries(c.talents.getEquippedAbilities.queryOptions()),n(null),l(null)},onError:s=>{console.error("Failed to equip ability:",s),j.error(s.message||"Failed to equip ability")}})),v=h(c.talents.unequipAbility.mutationOptions({onSuccess:()=>{u.invalidateQueries(c.talents.getEquippedAbilities.queryOptions()),n(null),l(null)},onError:s=>{console.error("Failed to unequip ability:",s),j.error(s.message||"Failed to unequip ability")}})),y=s=>{const i=p.findIndex(m=>m===s)+1;i>0&&v.mutate({slot:i})},N=s=>{const i={...s,image:s.image??void 0};a&&a===i.id?(n(null),l(null)):(n(i.id),l(i))},w=e?.talentList?.map(s=>s.talentInfo);return t.jsxs("div",{className:"p-2 md:mx-auto md:max-w-6xl",children:[t.jsx("p",{className:"text-slate-300",children:"Equipped Abilities"}),a&&t.jsx("div",{className:"overlayDim"}),t.jsx("div",{className:"mx-2 mt-2 grid grid-cols-2 grid-rows-2 gap-4 rounded-lg border-2 border-slate-500 bg-blue-900 p-2 shadow-xl",children:k.map((s,i)=>t.jsx("div",{className:o(a&&"cursor-pointer ring-2 ring-blue-300 hover:ring-blue-200","z-40 flex h-20 w-full rounded-md bg-slate-800"),onClick:()=>a&&f.mutate({talentId:a,slot:i+1}),children:s?t.jsx("img",{className:"m-auto w-20 cursor-pointer rounded-full p-2",src:b[s.name]?.image||s?.image||"",alt:"",onClick:m=>{m.stopPropagation(),l(s)}}):t.jsx("p",{className:"m-auto text-slate-300",children:"Empty"})},i))}),t.jsx("p",{className:"my-2 text-slate-300",children:"Available Abilities"}),t.jsx("div",{className:"grid grid-cols-5 gap-4",children:w?.map(s=>s&&!p?.includes(s?.id)&&t.jsx("div",{className:"z-40",children:t.jsx("img",{alt:"",src:b[s.name]?.image||(s.image??void 0)||"",className:o(a===s.id&&"z-40 ring-4 ring-blue-500","w-20 cursor-pointer rounded-full"),onClick:()=>N(s)})},s.id))}),d&&t.jsx(E,{abilityInfo:d,setAbilityInfo:l,setAbilitySelected:n,unequipAbility:{mutate:y},abilitySelected:a})]})}export{z as default};
