import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import createTestData from "./createTestData.js";
import * as PetsAdminController from "./pets.admin.js";
import { customizePet, evolvePet, feedPet, getPets, playPet, setActivePetStatus, trainPet } from "./pets.controller.js";
import petsSchema from "./pets.validation.js";
import z from "zod";

// Schema for set active pet endpoint
const setActivePetSchema = z.object({
    userPetId: z.number().int().positive(),
});

export const petsRouter = {
    /**
     * Get user's pets list
     */
    list: isLoggedInAuth.handler(async ({ context }) => {
        const pets = await getPets(context.user.id);
        return handleResponse({ data: pets });
    }),

    /**
     * Play with a pet
     */
    play: canMakeStateChangesAuth.input(petsSchema.playPetSchema).handler(async ({ input, context }) => {
        const { petId, gameType, duration } = input;
        const result = await playPet(context.user.id, petId, gameType, duration);
        return handleResponse({ data: result });
    }),

    /**
     * Train a pet
     */
    train: canMakeStateChangesAuth.input(petsSchema.trainPetSchema).handler(async ({ input, context }) => {
        const { petId, trainingType, duration } = input;
        const result = await trainPet(context.user.id, petId, trainingType, duration);
        return handleResponse({ data: result });
    }),

    /**
     * Customize a pet
     */
    customize: canMakeStateChangesAuth.input(petsSchema.customizePetSchema).handler(async ({ input, context }) => {
        const { petId, customization } = input;
        const updatedPet = await customizePet(context.user.id, petId, customization || {});
        return handleResponse({ data: updatedPet });
    }),

    /**
     * Evolve a pet
     */
    evolve: canMakeStateChangesAuth.input(petsSchema.evolvePetSchema).handler(async ({ input, context }) => {
        const { petId } = input;
        const evolvedPet = await evolvePet(context.user.id, petId);
        return handleResponse({ data: evolvedPet });
    }),

    /**
     * Feed a pet
     */
    feed: canMakeStateChangesAuth.input(petsSchema.feedPetSchema).handler(async ({ input, context }) => {
        const { petId, foodType, quantity } = input;
        const updatedPet = await feedPet(context.user.id, petId, foodType, quantity);
        return handleResponse({ data: updatedPet });
    }),

    /**
     * Set active pet status
     */
    setActive: canMakeStateChangesAuth.input(setActivePetSchema).handler(async ({ input, context }) => {
        const { userPetId } = input;
        const activePet = await setActivePetStatus(context.user.id, userPetId);
        return handleResponse({ data: activePet });
    }),
};

export const petsAdminRouter = {
    /**
     * Get all pets (admin)
     */
    list: adminAuth.handler(async () => {
        const data = await PetsAdminController.getAllPets();
        return handleResponse({ data });
    }),

    /**
     * Create a new pet (admin)
     */
    create: adminAuth
        .input(
            z.object({
                name: z.string().min(1),
                species: z.string().min(1),
                maxLevel: z.number().optional(),
                evolution_stages: z.array(z.any()).optional(),
            })
        )
        .handler(async ({ input }) => {
            const data = await PetsAdminController.createPet(input);
            return handleResponse({ data });
        }),

    /**
     * Update a pet (admin)
     */
    update: adminAuth
        .input(
            z.object({
                id: z.number().int().positive(),
                name: z.string().min(1).optional(),
                species: z.string().min(1).optional(),
                maxLevel: z.number().optional(),
                evolution_stages: z.array(z.any()).optional(),
            })
        )
        .handler(async ({ input }) => {
            const data = await PetsAdminController.updatePet(input);
            return handleResponse({ data });
        }),

    /**
     * Seed test data (admin)
     */
    seed: adminAuth.handler(async () => {
        const data = await createTestData();
        return handleResponse({ data });
    }),
};

export default petsRouter;
