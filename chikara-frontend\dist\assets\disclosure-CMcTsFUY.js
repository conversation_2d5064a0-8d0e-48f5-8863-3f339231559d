import{m as E,aP as T,r as l,aR as x,cM as G,aO as I,aT as h,cN as J,bc as L,b6 as k,bi as X,bj as Z,bd as C,bo as z,cO as Q,bt as W,bu as Y,bv as ee,bw as te,bn as V,bx as j,bD as le,by as g,aZ as se,b0 as A}from"./index-DjvF_jFD.js";var H;let ae=(H=E.startTransition)!=null?H:function(e){e()};var ne=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ne||{}),re=(e=>(e[e.ToggleDisclosure=0]="ToggleDisclosure",e[e.CloseDisclosure=1]="CloseDisclosure",e[e.SetButtonId=2]="SetButtonId",e[e.SetPanelId=3]="SetPanelId",e[e.SetButtonElement=4]="SetButtonElement",e[e.SetPanelElement=5]="SetPanelElement",e))(re||{});let oe={0:e=>({...e,disclosureState:k(e.disclosureState,{0:1,1:0})}),1:e=>e.disclosureState===1?e:{...e,disclosureState:1},2(e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},3(e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}},4(e,t){return e.buttonElement===t.element?e:{...e,buttonElement:t.element}},5(e,t){return e.panelElement===t.element?e:{...e,panelElement:t.element}}},w=l.createContext(null);w.displayName="DisclosureContext";function O(e){let t=l.useContext(w);if(t===null){let c=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,O),c}return t}let B=l.createContext(null);B.displayName="DisclosureAPIContext";function U(e){let t=l.useContext(B);if(t===null){let c=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,U),c}return t}let F=l.createContext(null);F.displayName="DisclosurePanelContext";function ue(){return l.useContext(F)}function ce(e,t){return k(t.type,oe,e,t)}let ie=l.Fragment;function de(e,t){let{defaultOpen:c=!1,...i}=e,u=l.useRef(null),d=x(t,G(r=>{u.current=r},e.as===void 0||e.as===l.Fragment)),p=l.useReducer(ce,{disclosureState:c?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:s,buttonId:a},S]=p,n=I(r=>{S({type:1});let f=se(u);if(!f||!a)return;let b=r?A(r)?r:"current"in r&&A(r.current)?r.current:f.getElementById(a):f.getElementById(a);b?.focus()}),v=l.useMemo(()=>({close:n}),[n]),m=l.useMemo(()=>({open:s===0,close:n}),[s,n]),y={ref:d},D=h();return E.createElement(w.Provider,{value:p},E.createElement(B.Provider,{value:v},E.createElement(J,{value:n},E.createElement(L,{value:k(s,{0:C.Open,1:C.Closed})},D({ourProps:y,theirProps:i,slot:m,defaultTag:ie,name:"Disclosure"})))))}let pe="button";function fe(e,t){let c=l.useId(),{id:i=`headlessui-disclosure-button-${c}`,disabled:u=!1,autoFocus:d=!1,...p}=e,[s,a]=O("Disclosure.Button"),S=ue(),n=S===null?!1:S===s.panelId,v=l.useRef(null),m=x(v,t,I(o=>{if(!n)return a({type:4,element:o})}));l.useEffect(()=>{if(!n)return a({type:2,buttonId:i}),()=>{a({type:2,buttonId:null})}},[i,a,n]);let y=I(o=>{var P;if(n){if(s.disclosureState===1)return;switch(o.key){case g.Space:case g.Enter:o.preventDefault(),o.stopPropagation(),a({type:0}),(P=s.buttonElement)==null||P.focus();break}}else switch(o.key){case g.Space:case g.Enter:o.preventDefault(),o.stopPropagation(),a({type:0});break}}),D=I(o=>{switch(o.key){case g.Space:o.preventDefault();break}}),r=I(o=>{var P;le(o.currentTarget)||u||(n?(a({type:0}),(P=s.buttonElement)==null||P.focus()):a({type:0}))}),{isFocusVisible:f,focusProps:b}=W({autoFocus:d}),{isHovered:$,hoverProps:R}=Y({isDisabled:u}),{pressed:M,pressProps:N}=ee({disabled:u}),_=l.useMemo(()=>({open:s.disclosureState===0,hover:$,active:M,disabled:u,focus:f,autofocus:d}),[s,$,M,f,u,d]),K=te(e,s.buttonElement),q=n?V({ref:m,type:K,disabled:u||void 0,autoFocus:d,onKeyDown:y,onClick:r},b,R,N):V({ref:m,id:i,type:K,"aria-expanded":s.disclosureState===0,"aria-controls":s.panelElement?s.panelId:void 0,disabled:u||void 0,autoFocus:d,onKeyDown:y,onKeyUp:D,onClick:r},b,R,N);return h()({ourProps:q,theirProps:p,slot:_,defaultTag:pe,name:"Disclosure.Button"})}let me="div",be=j.RenderStrategy|j.Static;function Ee(e,t){let c=l.useId(),{id:i=`headlessui-disclosure-panel-${c}`,transition:u=!1,...d}=e,[p,s]=O("Disclosure.Panel"),{close:a}=U("Disclosure.Panel"),[S,n]=l.useState(null),v=x(t,I($=>{ae(()=>s({type:5,element:$}))}),n);l.useEffect(()=>(s({type:3,panelId:i}),()=>{s({type:3,panelId:null})}),[i,s]);let m=X(),[y,D]=Z(u,S,m!==null?(m&C.Open)===C.Open:p.disclosureState===0),r=l.useMemo(()=>({open:p.disclosureState===0,close:a}),[p.disclosureState,a]),f={ref:v,id:i,...z(D)},b=h();return E.createElement(Q,null,E.createElement(F.Provider,{value:p.panelId},b({ourProps:f,theirProps:d,slot:r,defaultTag:me,features:be,visible:y,name:"Disclosure.Panel"})))}let Se=T(de),ye=T(fe),Ie=T(Ee),De=Object.assign(Se,{Button:ye,Panel:Ie});export{De as V};
