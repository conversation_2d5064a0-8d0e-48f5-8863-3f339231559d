import { api } from "@/helpers/api";
import { type UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface RemoveFriendParams {
    friendId: number;
}

interface RemoveFriendResponse {
    removed: boolean;
}

const useRemoveFriend = (
    options: Partial<UseMutationOptions<RemoveFriendResponse, Error, RemoveFriendParams>> = {}
) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.removeFriend.mutationOptions({
            onSuccess: () => {
                toast.success("Friend removed successfully");
                // Invalidate friends list to refresh the data
                queryClient.invalidateQueries({ queryKey: api.social.getFriends.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to remove friend");
            },
            ...options,
        })
    );
};

export default useRemoveFriend;
