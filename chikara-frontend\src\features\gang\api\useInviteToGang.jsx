import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-hot-toast";

const useInviteToGang = () => {
    const [studentId, setStudentId] = useState(undefined);
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.inviteMember.mutationOptions({
            onSuccess: () => {
                toast.success(`User invited to gang!`);
                queryClient.invalidateQueries({
                    queryKey: api.gang.getInviteList.key(),
                });
                setStudentId(undefined);
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const inviteToGang = () => {
        if (!studentId) {
            toast.error("Student ID is required");
            return;
        }

        mutation.mutate({ userId: parseInt(studentId) });
    };

    return {
        studentId,
        setStudentId,
        inviteToGang,
    };
};

export default useInviteToGang;
