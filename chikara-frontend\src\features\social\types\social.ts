import type { AppRouterClient } from "@/lib/orpc";

export type FriendListResponse = Awaited<ReturnType<AppRouterClient["social"]["getFriends"]>>;
export type Friend = FriendListResponse[number];

export type FriendRequestListResponse = Awaited<ReturnType<AppRouterClient["social"]["getFriendRequests"]>>;
export type FriendRequest = FriendRequestListResponse[number];

export type FriendRequestResponse = Awaited<ReturnType<AppRouterClient["social"]["respondToFriendRequest"]>>;

export type RivalListResponse = Awaited<ReturnType<AppRouterClient["social"]["getRivals"]>>;
export type Rival = RivalListResponse[number];
