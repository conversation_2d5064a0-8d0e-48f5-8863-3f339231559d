import type { RouterClient } from "@orpc/server";
import { bankRouter } from "./features/bank/bank.routes.js";
import { exploreRouter } from "./features/explore/explore.routes.js";
import { jobRouter, jobAdminRouter } from "./features/job/job.routes.js";
import userRouter from "./features/user/user.routes.js";
import talentsRouter from "./features/talents/talents.routes.js";
import { battleRouter } from "./features/battle/battle.routes.js";
import { storyRouter } from "./features/story/story.routes.js";
import { questRouter } from "./features/quest/quest.routes.js";
import { questAdminRouter } from "./features/quest/quest.admin.js";
import { auctionRouter } from "./features/auction/auction.routes.js";
import { shopRouter, shopAdminRouter } from "./features/shop/shop.routes.js";
import { gangRouter } from "./features/gang/gang.routes.js";
import { courseRouter } from "./features/course/course.routes.js";
import { suggestionsRouter } from "./features/suggestions/suggestions.routes.js";
import { craftingAdminRouter, craftingRouter } from "./features/crafting/crafting.routes.js";
import { privateMessageRouter } from "./features/privatemessage/privatemessage.routes.js";
import { profileCommentRouter } from "./features/profilecomment/profilecomment.routes.js";
import { chatRouter } from "./features/chat/chat.routes.js";
import { roguelikeRouter } from "./features/roguelike/roguelike.routes.js";
import { dailyQuestRouter } from "./features/dailyquest/dailyquest.routes.js";
import { infirmaryRouter } from "./features/infirmary/infirmary.routes.js";
import { jailRouter } from "./features/jail/jail.routes.js";
import { missionRouter } from "./features/mission/mission.routes.js";
import { propertyRouter } from "./features/property/property.routes.js";
import { petsAdminRouter, petsRouter } from "./features/pets/pets.routes.js";
import casinoRouter from "./features/casino/casino.routes.js";
import { bountyRouter } from "./features/bounty/bounty.routes.js";
import { notificationRouter } from "./features/notification/notification.routes.js";
import { authRouter, authAdminRouter } from "./features/auth/auth.routes.js";
import { creatureAdminRouter } from "./features/creature/creature.routes.js";
import { publicAuth } from "./lib/orpc.js";
import { itemRouter, itemAdminRouter } from "./features/item/item.routes.js";
import { shrineRouter } from "./features/shrine/shrine.routes.js";
import { leaderboardRouter } from "./features/leaderboard/leaderboard.routes.js";
import { dropChanceAdminRouter } from "./features/dropchance/dropchance.routes.js";
import { socialRouter } from "./features/social/social.routes.js";
import { skillsRouter } from "./features/skills/skills.routes.js";
import { devRouter } from "./features/dev/dev.routes.js";
import { actionlogRouter } from "./features/actionlog/actionlog.routes.js";
import { adminRouter } from "./features/admin/admin.routes.js";

export const appRouter = {
    healthCheck: publicAuth.handler(() => {
        return "OK";
    }),
    auth: authRouter,
    explore: exploreRouter,
    bank: bankRouter,
    job: jobRouter,
    user: userRouter,
    talents: talentsRouter,
    battle: battleRouter,
    story: storyRouter,
    quest: questRouter,
    dailyQuest: dailyQuestRouter,
    auction: auctionRouter,
    shop: shopRouter,
    gang: gangRouter,
    course: courseRouter,
    suggestions: suggestionsRouter,
    crafting: craftingRouter,
    privateMessage: privateMessageRouter,
    profileComment: profileCommentRouter,
    chat: chatRouter,
    roguelike: roguelikeRouter,
    infirmary: infirmaryRouter,
    jail: jailRouter,
    mission: missionRouter,
    property: propertyRouter,
    pets: petsRouter,
    casino: casinoRouter,
    bounty: bountyRouter,
    notification: notificationRouter,
    item: itemRouter,
    shrine: shrineRouter,
    leaderboard: leaderboardRouter,
    social: socialRouter,
    skills: skillsRouter,
    dev: devRouter,
    admin: {
        ...adminRouter,
        crafting: craftingAdminRouter,
        actionlog: actionlogRouter,
        quest: questAdminRouter,
        creature: creatureAdminRouter,
        auth: authAdminRouter,
        item: itemAdminRouter,
        dropChance: dropChanceAdminRouter,
        job: jobAdminRouter,
        pets: petsAdminRouter,
        shop: shopAdminRouter,
    },
};

export type AppRouterClient = RouterClient<typeof appRouter>;
