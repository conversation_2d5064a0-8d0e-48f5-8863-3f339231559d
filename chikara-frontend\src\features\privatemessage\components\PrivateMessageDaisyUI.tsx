import { DisplayAvatar } from "@/components/DisplayAvatar";
import { cn } from "@/lib/utils";
import { UTCDateMini } from "@date-fns/utc";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { useEffect } from "react";
import { api } from "@/helpers/api";
import { useNavigate } from "react-router-dom";
import RenderChatText from "../../chat/components/RenderChatText";
import { formatTimeToNow } from "@/helpers/dateHelpers";

export default function PrivateMessage({ message, type = "sentMessage", convoUserInfo, currentUser }) {
    const queryClient = useQueryClient();
    const isSentMessage = type === "sentMessage";
    const navigate = useNavigate();

    // Properly create a mutation for marking a message as read
    const { mutate: markMessageRead } = useMutation(
        api.messaging.markMessageRead.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.messaging.getChatHistory.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.messaging.getUnreadCount.key(),
                });
            },
            onError: (error) => {
                console.error("Failed to mark message as read:", error);
            },
        })
    );

    useEffect(() => {
        if (message?.read === false) {
            markMessageRead({ messageId: message.id });
        }
        // Re-run if read status changes
    }, [message?.read, markMessageRead]);

    if (!message) return null;

    return (
        <div key={message.id} className={cn("chat", isSentMessage ? "chat-end" : "chat-start")}>
            <div className="chat-image avatar">
                <div className="w-10 rounded-full">
                    <DisplayAvatar
                        className="cursor-pointer rounded-full"
                        src={isSentMessage ? currentUser : convoUserInfo}
                        onClick={() => navigate(`/profile/${isSentMessage ? currentUser?.id : convoUserInfo?.id}`)}
                    />
                </div>
            </div>
            {message.isGlobal && (
                <div
                    className={cn(
                        "chat-header text-xs text-stroke-s-sm font-display mb-1",
                        convoUserInfo.userType === "admin" ? " text-red-500" : " text-blue-500"
                    )}
                >
                    {convoUserInfo.userType === "admin" ? "Announcement" : "Megaphone"}
                </div>
            )}
            <div className={"chat-bubble chat-bubble-primary"}>
                <RenderChatText className="text-sm" msg={message} />

                <div className={cn("bottom-[-1.1rem] absolute", isSentMessage ? "right-0 " : "left-0 ")}>
                    <p
                        data-tooltip-id="date-tooltip"
                        data-tooltip-content={format(new UTCDateMini(message.createdAt), "PP, p")}
                        className="cursor-pointer whitespace-nowrap font-body font-semibold text-slate-300 text-stroke-0 text-xs sm:mt-0"
                    >
                        <time className="hidden group-hover:block" dateTime={message.createdAt}>
                            {format(new UTCDateMini(message.createdAt), "PP, p")}
                        </time>
                        <time className="group-hover:hidden" dateTime={message.createdAt}>
                            {formatTimeToNow(message.createdAt)} ago
                        </time>
                    </p>
                </div>
            </div>
        </div>
    );
}
