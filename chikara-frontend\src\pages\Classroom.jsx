import { UsersTable } from "@/components/UsersTable";
import ClassChatroom from "@/features/classroom/components/ClassChatroom";
import ClassShop from "@/features/classroom/components/ClassShop";
import ClassTabs from "@/features/classroom/components/ClassTabs";
import ExamRanking from "@/features/classroom/components/ExamRanking";
import ExamsPage from "@/features/classroom/components/ExamsTab";
import { api } from "@/helpers/api";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";

export default function Classroom() {
    // Disable classroom for now
    return null;
}

// export default function Classroom() {
//     const [selectedTab, setSelectedTab] = useState("exams");

//     const { isLoading, data } = useQuery({
//         queryKey: api.user.getUserList,
//         keepPreviousData: true,
//         select: (d) => d?.filter((user) => user.userType !== "admin"),
//     });
//     const { data: currentUser } = useFetchCurrentUser();

//     const currentClass = currentUser?.class;

//     const classroomChatboxes = {
//         Mizu: { id: 3, name: "mizu" },
//         Honoo: { id: 4, name: "honoo" },
//         Kaze: { id: 5, name: "kaze" },
//         Tsuchi: { id: 6, name: "tsuchi" },
//     };

//     const currentChatroom = classroomChatboxes[currentClass];

//     const classList = data?.filter((el) => el.class === currentClass) || [];

//     return (
//         <div className="mx-auto max-w-(--breakpoint-md) rounded-t-lg bg-[#14191E]">
//             <div className="relative mb-2 flex h-12 justify-between border-black border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:rounded-t-lg">
//                 <h1 className="my-auto font-body font-semibold text-lg text-stroke-s-sm text-white">
//                     {currentUser?.class} Classroom
//                 </h1>
//                 <div className="flex items-center justify-center gap-2 p-1 text-custom-yellow text-xl">
//                     <img
//                         className="h-full w-auto"
//                         src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Ou05yxV.png`}
//                         alt=""
//                     />
//                     <p>{currentUser?.classPoints || 0}</p>
//                 </div>

//                 <div className="absolute bottom-0 left-0 h-1 w-full bg-[#272839]"></div>
//             </div>
//             <ClassTabs selectedTab={selectedTab} setSelectedTab={setSelectedTab} currentUser={currentUser} />
//             <div className="boxtest">
//                 <div className="innerBoxTest relative mx-auto flex w-full flex-col gap-2 rounded-b-md font-body text-stroke-sm lg:p-[20px]">
//                     {selectedTab === "exams" && <ExamsPage />}
//                     {selectedTab === "members" && <ClassMembers classList={classList} isLoading={isLoading} />}
//                     {selectedTab === "chat" && (
//                         <ClassChatroom currentClass={currentClass} currentChatroom={currentChatroom} />
//                     )}
//                     {selectedTab === "shop" && <ClassShop currentUser={currentUser} />}
//                     {selectedTab === "rankings" && <ExamRanking />}
//                 </div>
//             </div>
//         </div>
//     );
// }

// const ClassMembers = ({ classList, isLoading }) => {
//     const sortedList = classList.sort((a, b) => (b.classPoints > a.classPoints ? 1 : -1));
//     return (
//         <div className="flex w-full gap-4">
//             <div className="w-full md:mx-auto">
//                 <UsersTable data={sortedList} isLoading={isLoading} type="class" className="rounded-md!" />
//             </div>
//         </div>
//     );
// };
