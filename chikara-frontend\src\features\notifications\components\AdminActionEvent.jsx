import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Flame } from "lucide-react";
import { Link } from "react-router-dom";

function AdminActionEvent({ details, read }) {
    const { senderId, item, amount, message, type } = details;
    const { data: user } = useGetUserInfo(senderId);
    if (type === "chat_ban") {
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                        !read && "bg-blue-50 dark:bg-blue-900"
                    }`}
                >
                    <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 font-bold text-gray-500 text-stroke-0 text-xs md:text-sm dark:text-red-500 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    You were banned from chat for {details?.duration} minutes by <PERSON><PERSON><PERSON> for {details?.reason}
                </td>
            </>
        );
    }
    if (type === "jail") {
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                        !read && "bg-blue-50 dark:bg-blue-900"
                    }`}
                >
                    <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 font-bold text-gray-500 text-stroke-0 text-xs md:text-sm dark:text-red-500 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    You were jailed for {details?.duration} minutes by Haruka Ito for {details?.reason}
                </td>
            </>
        );
    }
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                You received a gift of <span className="text-custom-yellow">{amount}x</span>{" "}
                <span className="text-custom-yellow">{item.name}</span> from{" "}
                <Link className="text-blue-500" to={`/profile/${senderId}`}>
                    {user?.username}
                </Link>{" "}
                {message && <span>for {message}</span>}
            </td>
        </>
    );
}

export default AdminActionEvent;
