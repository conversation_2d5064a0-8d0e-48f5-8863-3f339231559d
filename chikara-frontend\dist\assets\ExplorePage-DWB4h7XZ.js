import{C as P,dd as ot,de as Ge,df as nt,dg as lt,r as p,h as u,x as Qe,j as e,aE as G,dh as te,di as Fe,X as He,dj as me,d2 as ct,d3 as dt,d4 as ut,d5 as mt,dk as ht,v as he,bz as xt,m as Se,e as Y,a as J,ai as Ue,g as q,c as S,dl as xe,aL as gt,Z as We,aw as pt,d9 as bt,bM as M,aA as ae,ad as re,t as ke,aD as ft,dm as Ce,B as ge,dn as yt,dp as vt,cS as ee,cR as wt,aF as jt,ay as Te,b as Je,dq as Nt,dr as kt,ds as Ct,dt as At,du as It,bX as qe,dv as St,ar as Ve,l as X,A as Tt,dw as Et,J as Ot,M as Mt,dx as zt}from"./index-DjvF_jFD.js";import{C as Ae}from"./circle-check-big-sPkhu49B.js";import{C as Ee}from"./circle-question-mark-DWoxCUQ0.js";import{P as Dt,W as Lt}from"./wallet-DNtQv1cu.js";import{C as Ke}from"./circle-alert-CXfSS0tn.js";import{r as Ie}from"./rarityColours-CxbqwiYb.js";import{A as de}from"./arrow-left-CtVOn__D.js";import{B as Bt}from"./book-open-L7OJXLkS.js";import{M as Z}from"./map-pin-BTHqkeyj.js";import{T as Rt}from"./trending-up-BXbYcQRT.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pt=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],Yt=P("book",Pt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gt=[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Qt=P("compass",Gt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ft=[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]],Ht=P("leaf",Ft);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],Wt=P("list",Ut);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],ue=P("loader-circle",Jt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]],Xe=P("map",qt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vt=[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]],Kt=P("navigation",Vt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],_e=P("play",Xt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=[["circle",{cx:"6",cy:"19",r:"3",key:"1kj8tv"}],["path",{d:"M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15",key:"1d8sl"}],["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}]],Zt=P("route",_t);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $t=[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]],er=P("shopping-bag",$t);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]],rr=P("skip-forward",tr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=[["rect",{width:"16",height:"16",x:"4",y:"3",rx:"2",key:"1wxw4b"}],["path",{d:"M4 11h16",key:"mpoxn0"}],["path",{d:"M12 3v8",key:"1h2ygw"}],["path",{d:"m8 19-2 3",key:"13i0xs"}],["path",{d:"m18 22-2-3",key:"1p0ohu"}],["path",{d:"M8 15h.01",key:"a7atzg"}],["path",{d:"M16 15h.01",key:"rnfrdf"}]],je=P("tram-front",ar);function sr(t){t.values.forEach(r=>r.stop())}function Ne(t,r){[...r].reverse().forEach(s=>{const i=t.getVariant(s);i&&Ge(t,i),t.variantChildren&&t.variantChildren.forEach(n=>{Ne(n,r)})})}function ir(t,r){if(Array.isArray(r))return Ne(t,r);if(typeof r=="string")return Ne(t,[r]);Ge(t,r)}function or(){const t=new Set,r={subscribe(a){return t.add(a),()=>void t.delete(a)},start(a,s){const i=[];return t.forEach(n=>{i.push(ot(n,a,{transitionOverride:s}))}),Promise.all(i)},set(a){return t.forEach(s=>{ir(s,a)})},stop(){t.forEach(a=>{sr(a)})},mount(){return()=>{r.stop()}}};return r}function nr(){const t=nt(or);return lt(t.mount,[]),t}const pe=nr,O={SIZE:5,MIN_COORD:0,MAX_COORD:4};function _(t,r=800,a=600,s){if(t.x<O.MIN_COORD||t.x>O.MAX_COORD||t.y<O.MIN_COORD||t.y>O.MAX_COORD)return console.warn(`Invalid grid position: ${t.x}, ${t.y}`),{x:80,y:80};let i=Math.max(60,Math.min(r,a)*.1),n=r-2*i,l=a-2*i,m=n/(O.SIZE-1),c=l/(O.SIZE-1);const g=50;if(m<g||c<g){const b=(r-g*(O.SIZE-1))/2,x=(a-g*(O.SIZE-1))/2;i=Math.max(50,Math.min(b,x)),n=r-2*i,l=a-2*i,m=n/(O.SIZE-1),c=l/(O.SIZE-1)}const h=i+t.x*m,d=i+t.y*c;return{x:Math.round(h),y:Math.round(d)}}function lr(t,r,a="curved"){const s=t,i=r;let n,l=[];switch(a){case"straight":n=`M ${t.x} ${t.y} L ${r.x} ${r.y}`;break;case"curved":n=cr(t,r);break;case"bezier":const c=dr(t,r);n=c.path,l=c.controlPoints;break;default:n=`M ${t.x} ${t.y} L ${r.x} ${r.y}`}const m=ur(t,r);return{d:n,length:m,startPoint:s,endPoint:i,controlPoints:l}}function cr(t,r){const a=r.x-t.x,s=r.y-t.y,i=t.x+a*.5,n=t.y+s*.3-Math.abs(a)*.2;return`M ${t.x} ${t.y} Q ${i} ${n} ${r.x} ${r.y}`}function dr(t,r){const a=r.x-t.x,s=r.y-t.y,i={x:t.x+a*.25,y:t.y+s*.1},n={x:t.x+a*.75,y:t.y+s*.9};return{path:`M ${t.x} ${t.y} C ${i.x} ${i.y} ${n.x} ${n.y} ${r.x} ${r.y}`,controlPoints:[i,n]}}function ur(t,r){const a=r.x-t.x,s=r.y-t.y;return Math.sqrt(a*a+s*s)}function mr(t,r){const a=r.x-t.x,s=r.y-t.y;return Math.atan2(s,a)}function Ze(t){if(t.length===0)return{minX:0,minY:0,maxX:0,maxY:0,width:0,height:0};const r=t.map(l=>l.position),a=Math.min(...r.map(l=>l.x)),s=Math.min(...r.map(l=>l.y)),i=Math.max(...r.map(l=>l.x)),n=Math.max(...r.map(l=>l.y));return{minX:a,minY:s,maxX:i,maxY:n,width:i-a,height:n-s}}function hr(t,r,a,s=50){const i=Ze(t);if(i.width===0||i.height===0)return t;const n=(r-2*s)/i.width,l=(a-2*s)/i.height,m=Math.min(n,l);return t.map(c=>({...c,position:{x:(c.position.x-i.minX)*m+s,y:(c.position.y-i.minY)*m+s}}))}function xr(t,r){return r==="isolated"?[]:t}function gr(t,r){return r==="isolated"?t.map(a=>({...a,status:a.status==="locked"?"locked":"available"})):t}const pr=Qe(["relative flex items-center justify-center","border-2 rounded-full cursor-pointer","transition-all duration-300 ease-in-out","font-medium text-sm","shadow-lg backdrop-blur-xs","select-none","hover:scale-110 hover:shadow-xl","hover:z-10","focus:outline-hidden focus:ring-2 focus:ring-offset-2"],{variants:{status:{completed:["bg-green-500/90 border-green-400","text-white shadow-green-500/25","hover:bg-green-400 hover:border-green-300","focus:ring-green-500","after:absolute after:inset-0 after:rounded-full","after:bg-green-400/20 after:blur-xs after:-z-10"],available:["bg-blue-500/90 border-blue-400","text-white shadow-blue-500/25","hover:bg-blue-400 hover:border-blue-300","focus:ring-blue-500"],locked:["bg-gray-400/60 border-gray-500","text-gray-300 shadow-gray-500/25","cursor-not-allowed opacity-75","hover:scale-100 hover:shadow-lg","focus:ring-gray-500"],current:["bg-yellow-500/90 border-yellow-400","text-white shadow-yellow-500/25","hover:bg-yellow-400 hover:border-yellow-300","focus:ring-yellow-500","after:absolute after:inset-0 after:rounded-full","after:bg-yellow-400/30 after:blur-md after:-z-10","before:absolute before:inset-0 before:rounded-full","before:bg-yellow-400/20 before:animate-ping before:-z-10"]},size:{small:"w-8 h-8 text-xs",medium:"w-12 h-12 text-sm",large:"w-16 h-16 text-base",xlarge:"w-20 h-20 text-lg"},nodeType:{STORY:"border-dashed",CHOICE:"border-double border-4",CONDITION:"border-dotted",ACTION:"border-solid",BATTLE:"border-solid shadow-red-500/20",CHARACTER_ENCOUNTER:"border-solid shadow-purple-500/20",SHOP:"border-solid shadow-orange-500/20",HOUSING:"border-solid shadow-yellow-500/20",MINING_NODE:"border-solid shadow-stone-500/20",SCAVENGE_NODE:"border-solid shadow-emerald-500/20",FORAGING_NODE:"border-solid shadow-lime-500/20"}},defaultVariants:{status:"locked",size:"medium",nodeType:"STORY"}});function br(t){switch(t){case"STORY":return"📖";case"CHOICE":return"🔀";case"CONDITION":return"❓";case"ACTION":return"⚡";case"BATTLE":return"⚔️";case"CHARACTER_ENCOUNTER":return"👥";case"SHOP":return"💰";case"HOUSING":return"🏠";case"MINING_NODE":return"⛏️";case"SCAVENGE_NODE":return"🔍";case"FORAGING_NODE":return"🌱";default:return"●"}}function fr(t){switch(t){case"completed":return"Completed";case"available":return"Available";case"locked":return"Locked";case"current":return"Current";default:return"Unknown"}}const yr=({node:t,position:r,isSelected:a=!1,showLabel:s=!0,showTypeIcon:i=!0,onClick:n,onMouseEnter:l,onMouseLeave:m,className:c,style:g,debugMode:h=!1,status:d,size:b,nodeType:x})=>{const[w,C]=p.useState(!1),v=d||t.status,o=b||"medium",A=x||t.nodeType,k=o==="small"?16:o==="medium"?24:o==="large"?32:40,j=p.useCallback(L=>{L.preventDefault(),L.stopPropagation(),v!=="locked"&&n&&n(t.id)},[v,n,t.id]),B=p.useCallback(()=>{C(!0),l&&l(t.id)},[l,t.id]),f=p.useCallback(()=>{C(!1),m&&m()},[m]),E=u(pr({status:v,size:o,nodeType:A}),a&&"ring-4 ring-white ring-opacity-60",c),T={position:"absolute",left:r.x-k,top:r.y-k,zIndex:w||a?20:10,...g};return e.jsxs("div",{className:"relative",children:[e.jsxs("button",{className:E,style:T,disabled:v==="locked","aria-label":`${t.title} - ${fr(v)}`,"aria-describedby":`node-${t.id}-description`,role:"button",tabIndex:v==="locked"?-1:0,onClick:j,onMouseEnter:B,onMouseLeave:f,children:[e.jsx("div",{className:"flex flex-col items-center justify-center",children:i&&e.jsx("span",{className:"text-xs opacity-90 scale-200",children:br(A)})}),t.expiresAt&&!t.isStatic&&e.jsx("div",{className:u("absolute -top-1 -right-1 w-4 h-4 rounded-full border border-white","flex items-center justify-center text-xs font-bold",te(t.expiresAt)?"bg-orange-500 text-white":"bg-blue-500 text-white"),children:e.jsx(G,{className:"w-2 h-2"})})]}),s&&e.jsxs("div",{className:"absolute pointer-events-none z-30 flex flex-col items-center",style:{left:r.x,top:r.y+k+8,transform:"translateX(-50%)"},children:[e.jsx("div",{className:u("bg-white/95 dark:bg-gray-900/95 text-gray-900 dark:text-white","px-2 py-1 rounded-md shadow-lg backdrop-blur-sm","font-medium text-center whitespace-nowrap","border border-gray-200/50 dark:border-gray-700/50",t.title.length<=8?"text-xs":t.title.length<=12?"text-[10px]":"text-[9px]",w&&"bg-white dark:bg-gray-800 shadow-xl"),children:t.title}),t.expiresAt&&!t.isStatic&&e.jsxs("div",{className:u("mt-1 px-2 py-0.5 rounded text-xs flex items-center gap-1","bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm","border border-gray-200/50 dark:border-gray-700/50",te(t.expiresAt)?"text-orange-600 dark:text-orange-400":"text-blue-600 dark:text-blue-400"),children:[e.jsx(G,{className:"w-2.5 h-2.5"}),e.jsx("span",{className:"font-medium",children:Fe(t.expiresAt)})]}),h&&e.jsxs("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400 bg-white/90 dark:bg-gray-900/90 px-2 py-0.5 rounded border border-gray-200/50 dark:border-gray-700/50",children:[e.jsxs("div",{children:["Type: ",A]}),e.jsxs("div",{children:["Status: ",v]}),e.jsxs("div",{children:["Pos: (",r.x,", ",r.y,")"]}),e.jsxs("div",{children:["ID: ",t.id]})]})]}),e.jsxs("div",{id:`node-${t.id}-description`,className:"sr-only",children:[t.description,". Type: ",A,"."]})]})};function vr(t){switch(t){case"completed":return{icon:Ae,color:"text-green-400"};case"available":return{icon:_e,color:"text-blue-400"};case"locked":return{icon:me,color:"text-gray-400"};case"current":return{icon:G,color:"text-yellow-400"};default:return{icon:G,color:"text-gray-400"}}}function wr(t){switch(t){case"STORY":return"📖";case"CHOICE":return"🔀";case"CONDITION":return"❓";case"ACTION":return"⚡";case"BATTLE":return"⚔️";case"CHARACTER_ENCOUNTER":return"👥";default:return"●"}}function jr(t){switch(t){case"completed":return"Completed";case"available":return"Available";case"locked":return"Locked";case"current":return"Current";default:return"Unknown"}}function Nr(t){return t.replace(/_/g," ").toLowerCase().replace(/\b\w/g,r=>r.toUpperCase())}const kr=({node:t,isOpen:r,onClose:a,onAccessNode:s})=>{if(!t)return null;const{icon:i,color:n}=vr(t.status),l=t.status==="available"||t.status==="current";return e.jsx(Cr,{open:r,onOpenChange:a,children:e.jsx(Ar,{children:e.jsxs("div",{className:"bg-gray-900 rounded-t-lg p-4 border-t border-x border-gray-700/50 max-h-[80vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"shrink-0 text-2xl",children:wr(t.nodeType)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:t.title}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(i,{className:u("w-4 h-4",n)}),e.jsx("span",{className:u("font-medium",n),children:jr(t.status)})]})]})]}),e.jsx("button",{className:"rounded-full p-1 bg-gray-800 hover:bg-gray-700 transition-colors",onClick:a,children:e.jsx(He,{className:"size-5 text-gray-400"})})]}),e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-300 leading-relaxed",children:t.description})}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm mb-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-gray-400",children:"Type"}),e.jsx("div",{className:"text-white font-medium",children:Nr(t.nodeType)})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-gray-400",children:"Node ID"}),e.jsxs("div",{className:"text-white font-medium",children:["#",t.id]})]})]}),t.connections&&t.connections.length>0&&e.jsxs("div",{className:"text-sm mb-4",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Connected to"}),e.jsxs("div",{className:"text-white",children:[t.connections.length," node",t.connections.length!==1?"s":""]})]}),e.jsx("button",{disabled:!l,className:u("w-full py-3 px-4 rounded-lg font-medium text-sm","transition-all duration-200 ease-out",l?["bg-blue-600 hover:bg-blue-500 text-white","hover:shadow-lg hover:shadow-blue-500/25","active:scale-95"]:["bg-gray-700 text-gray-400 cursor-not-allowed"]),onClick:()=>l&&s?.(t.id),children:t.status==="completed"?"Review Node":t.status==="current"?"Continue":t.status==="available"?"Access Node":"Locked"})]})})})},Cr=({children:t,...r})=>e.jsx(ct,{...r,children:e.jsxs(dt,{children:[e.jsx(ut,{className:"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"}),t]})}),Ar=({children:t,...r})=>e.jsxs(mt,{...r,className:u("fixed inset-x-0 bottom-0 z-500 mt-24 flex flex-col rounded-t-lg border border-gray-700/50 bg-gray-950","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0","data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom","duration-300"),children:[e.jsx("div",{className:"mx-auto w-12 h-1.5 shrink-0 rounded-full bg-gray-700 my-2"}),t]});function Ir(t){switch(t){case"completed":return{icon:Ae,color:"text-green-400"};case"available":return{icon:_e,color:"text-blue-400"};case"locked":return{icon:me,color:"text-gray-400"};case"current":return{icon:G,color:"text-yellow-400"};default:return{icon:G,color:"text-gray-400"}}}function Sr(t){switch(t){case"STORY":return"📖";case"CHOICE":return"🔀";case"CONDITION":return"❓";case"ACTION":return"⚡";case"BATTLE":return"⚔️";case"CHARACTER_ENCOUNTER":return"👥";default:return"●"}}function Tr(t){switch(t){case"completed":return"Completed";case"available":return"Available";case"locked":return"Locked";case"current":return"Current";default:return"Unknown"}}const Er=({node:t,position:r,isVisible:a,onClose:s,containerRef:i,zIndex:n=1e3})=>{const[l,m]=p.useState(!1);if(p.useEffect(()=>{if(a)m(!0);else{const h=setTimeout(()=>m(!1),200);return()=>clearTimeout(h)}},[a]),p.useEffect(()=>{if(!a||!i.current)return;const h=b=>{b.target.closest(".node-tooltip")||s()},d=i.current;return d.addEventListener("mousedown",h),()=>d.removeEventListener("mousedown",h)},[a,s,i]),p.useEffect(()=>{if(!a)return;const h=d=>{d.key==="Escape"&&s()};return document.addEventListener("keydown",h),()=>document.removeEventListener("keydown",h)},[a,s]),!t||!l||!r)return null;const{icon:c,color:g}=Ir(t.status);return e.jsxs("div",{className:u("node-tooltip absolute bg-gray-900/95 backdrop-blur-xs","border border-gray-700/50 rounded-lg shadow-2xl","w-80 max-w-[90vw] pointer-events-auto","transition-all duration-200 ease-out",a?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-95 translate-y-2"),style:{left:r.x,top:r.y,zIndex:n},children:[e.jsxs("div",{className:"relative p-4 pb-3 border-b border-gray-800/50",children:[e.jsx("button",{"aria-label":"Close tooltip",className:u("absolute top-3 right-3 p-1 rounded-full","text-gray-400 hover:text-white hover:bg-gray-800/50","transition-colors duration-150","focus:outline-hidden focus:ring-2 focus:ring-gray-600"),onClick:s,children:e.jsx(He,{className:"w-4 h-4"})}),e.jsxs("div",{className:"flex items-start gap-3 pr-8",children:[e.jsx("div",{className:"shrink-0 text-2xl",children:Sr(t.nodeType)}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-1 truncate",children:t.title}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(c,{className:u("w-4 h-4",g)}),e.jsx("span",{className:u("font-medium",g),children:Tr(t.status)})]})]})]})]}),e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsx("div",{children:e.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:t.description})}),t.expiresAt&&!t.isStatic&&t.nodeType!=="STORY"&&e.jsxs("div",{className:u("p-3 rounded-lg border text-sm",te(t.expiresAt)?"bg-orange-900/30 border-orange-600/50 text-orange-200":"bg-blue-900/30 border-blue-600/50 text-blue-200"),children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(G,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:te(t.expiresAt)?"Expiring Soon":"Expires In"})]}),e.jsx("div",{className:"text-white font-medium",children:ht(t.expiresAt)})]}),t.connections&&t.connections.length>0&&e.jsxs("div",{className:"text-sm",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Connected to"}),e.jsxs("div",{className:"text-white",children:[t.connections.length," node",t.connections.length!==1?"s":""]})]})]})]})},Or={small:32,medium:48,large:64,xlarge:80},Mr=({node:t,position:r,size:a,isSelected:s=!1,showLabel:i=!0,showTypeIcon:n=!0,onClick:l,onMouseEnter:m,onMouseLeave:c,onAccessNode:g,containerRef:h,viewport:d,className:b,style:x,debugMode:w=!1})=>{const[C,v]=p.useState(!1),[o,A]=p.useState(!1),[k,j]=p.useState(null),B=p.useRef(null),f=he(),E=p.useCallback(D=>{l?.(D),(t.status==="available"||t.status==="current")&&g&&g(D),f&&t.status!=="locked"&&A(!0)},[l,g,t.status,f]),T=p.useCallback(D=>{if(m?.(D),f||t.status==="locked"||!h?.current||!d)return;const F=h.current.getBoundingClientRect(),W=r.x*d.scale+d.x,se=r.y*d.scale+d.y,ie=320,$=280,oe=Or[a||"medium"],y=16;let I=W+oe/2+10,N=se-$/2;I+ie>F.width-y&&(I=W-oe/2-ie-10),I<y&&(I=y),N<y?N=y:N+$>F.height-y&&(N=F.height-$-y),j({x:I,y:N}),v(!0)},[m,f,t.status,h,d,r,a]),L=p.useCallback(()=>{c?.(),v(!1)},[c]),Q=p.useCallback(()=>{v(!1)},[]),R=p.useCallback(()=>{A(!1)},[]),V=p.useCallback(D=>{v(!1),A(!1),g&&g(D)},[g]);return e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:B,children:e.jsx(yr,{node:t,position:r,size:a,isSelected:s,showLabel:i,showTypeIcon:n,className:b,style:x,debugMode:w,onClick:E,onMouseEnter:T,onMouseLeave:L})}),!f&&h?.current&&xt.createPortal(e.jsx(Er,{node:t,position:k,isVisible:C,containerRef:h,zIndex:1e3,onClose:Q}),h.current.querySelector('div[style*="z-index: 1000"]')||h.current),f&&e.jsx(kr,{node:t,isOpen:o,onClose:R,onAccessNode:V})]})},Oe=({id:t,color:r,size:a=6})=>e.jsx("defs",{children:e.jsx("marker",{id:t,markerWidth:a*2,markerHeight:a*2,refX:a,refY:a,orient:"auto",markerUnits:"strokeWidth",children:e.jsx("polygon",{points:`0,0 ${a*2},${a} 0,${a*2}`,fill:r,className:"drop-shadow-xs"})})});function zr(t,r){const a="transition-all duration-300 ease-in-out";return t?{className:u(a,"stroke-blue-400",r&&"animate-pulse"),strokeWidth:3,strokeDasharray:r?"8,4":void 0,opacity:.8,color:"#60a5fa"}:{className:u(a,"stroke-gray-400"),strokeWidth:2,strokeDasharray:"4,4",opacity:.4,color:"#9ca3af"}}function Dr(t,r,a){return lr(t,r,a)}const Lr=({connection:t,isUnlocked:r=!0,showArrows:a=!0,animated:s=!1,variant:i="curved",className:n,debugMode:l=!1})=>{const m=p.useMemo(()=>Dr(t.fromPosition,t.toPosition,i),[t.fromPosition,t.toPosition,i]),c=p.useMemo(()=>zr(r,s),[r,s]);p.useMemo(()=>mr(t.fromPosition,t.toPosition),[t.fromPosition,t.toPosition]);const g=`arrow-${t.id}`,h=`arrow-glow-${t.id}`,d=p.useMemo(()=>({x:(t.fromPosition.x+t.toPosition.x)/2,y:(t.fromPosition.y+t.toPosition.y)/2}),[t.fromPosition,t.toPosition]);return e.jsxs("g",{className:u("connection-group",n),children:[a&&e.jsxs(e.Fragment,{children:[e.jsx(Oe,{id:g,color:c.color}),r&&e.jsx(Oe,{id:h,color:c.color,size:8})]}),r&&e.jsx("path",{d:m.d,className:"stroke-current opacity-20",strokeWidth:c.strokeWidth+4,stroke:c.color,fill:"none",filter:"blur(2px)"}),e.jsx("path",{d:m.d,className:c.className,strokeWidth:c.strokeWidth,strokeDasharray:c.strokeDasharray,opacity:c.opacity,fill:"none",markerEnd:a?`url(#${g})`:void 0,children:s&&r&&e.jsx("animate",{attributeName:"stroke-dashoffset",values:"0;12",dur:"1s",repeatCount:"indefinite"})}),e.jsx("path",{d:m.d,className:"stroke-transparent hover:stroke-blue-300 cursor-pointer",strokeWidth:c.strokeWidth+8,fill:"none",opacity:0,children:e.jsxs("title",{children:["Connection from Node ",t.fromNodeId," to Node ",t.toNodeId,t.conditionType&&` (${t.conditionType})`]})}),l&&e.jsxs("g",{className:"debug-info",children:[e.jsx("text",{x:d.x,y:d.y,className:"text-xs fill-gray-500 pointer-events-none",textAnchor:"middle",dominantBaseline:"middle",children:t.id}),e.jsx("circle",{cx:t.fromPosition.x,cy:t.fromPosition.y,r:3,className:"fill-green-500 opacity-60"}),e.jsx("circle",{cx:t.toPosition.x,cy:t.toPosition.y,r:3,className:"fill-red-500 opacity-60"}),m.controlPoints?.map((b,x)=>e.jsx("circle",{cx:b.x,cy:b.y,r:2,className:"fill-yellow-500 opacity-60"},x))]}),t.conditionType&&t.conditionType!=="ALL"&&e.jsxs("g",{className:"condition-indicator",children:[e.jsx("circle",{cx:d.x,cy:d.y-20,r:8,className:"fill-purple-500 opacity-80"}),e.jsx("text",{x:d.x,y:d.y-20,className:"text-xs fill-white font-bold pointer-events-none",textAnchor:"middle",dominantBaseline:"middle",children:"?"})]})]})},Br=({connections:t,viewportWidth:r,viewportHeight:a,animated:s=!1,variant:i="curved",debugMode:n=!1,className:l})=>e.jsx("svg",{width:r,height:a,className:u("absolute inset-0 pointer-events-none","overflow-visible",l),style:{zIndex:1},children:t.map(m=>e.jsx(Lr,{connection:m,isUnlocked:m.isUnlocked,animated:s,variant:i,debugMode:n},m.id))}),Rr=({containerWidth:t=800,containerHeight:r=600,showCoordinates:a=!1,showReservedPositions:s=!1,reservedPositions:i=[],className:n,opacity:l=.3})=>{const m=Se.useMemo(()=>{const h=[];for(let d=O.MIN_COORD;d<=O.MAX_COORD;d++)for(let b=O.MIN_COORD;b<=O.MAX_COORD;b++){const x=_({x:d,y:b},t,r);h.push({gridX:d,gridY:b,pixelX:x.x,pixelY:x.y})}return h},[t,r]),c=(h,d)=>i.some(b=>b.x===h&&b.y===d),g=Se.useMemo(()=>{const h=[];for(let d=O.MIN_COORD;d<=O.MAX_COORD;d++){const b=_({x:d,y:0},t,r),x=_({x:d,y:4},t,r);h.push({type:"vertical",x1:b.x,y1:b.y,x2:x.x,y2:x.y})}for(let d=O.MIN_COORD;d<=O.MAX_COORD;d++){const b=_({x:0,y:d},t,r),x=_({x:4,y:d},t,r);h.push({type:"horizontal",x1:b.x,y1:b.y,x2:x.x,y2:x.y})}return h},[t,r]);return e.jsxs("div",{className:u("absolute inset-0 pointer-events-none overflow-hidden",n),style:{zIndex:5},children:[e.jsx("svg",{width:"100%",height:"100%",viewBox:`0 0 ${t} ${r}`,className:"absolute inset-0",style:{opacity:l},children:g.map((h,d)=>e.jsx("line",{x1:h.x1,y1:h.y1,x2:h.x2,y2:h.y2,stroke:"currentColor",strokeWidth:1,strokeDasharray:"2,2",className:"text-blue-400"},`${h.type}-${d}`))}),m.map(h=>{const d=s&&c(h.gridX,h.gridY);return e.jsxs("div",{className:"absolute",style:{left:h.pixelX-8,top:h.pixelY-8,transform:"translate(-50%, -50%)"},children:[e.jsx("div",{style:{opacity:l+.2},className:u("w-4 h-4 rounded-full border-2 transition-colors",d?"bg-red-500/30 border-red-500":"bg-blue-500/20 border-blue-500")}),a&&e.jsxs("div",{className:u("absolute text-xs font-mono px-1 py-0.5 rounded","bg-black/70 text-white whitespace-nowrap",d?"text-red-300":"text-blue-300"),style:{left:"50%",top:"100%",transform:"translateX(-50%)",marginTop:"4px",opacity:l+.3},children:["(",h.gridX,",",h.gridY,")"]}),d&&e.jsx("div",{className:"absolute text-xs font-bold text-red-500",style:{left:"50%",top:"50%",transform:"translate(-50%, -50%)",opacity:l+.4},children:"R"})]},`${h.gridX}-${h.gridY}`)}),(a||s)&&e.jsxs("div",{className:"absolute top-4 right-4 bg-black/80 text-white p-3 rounded text-xs font-mono",style:{opacity:l+.5},children:[e.jsx("div",{className:"font-bold mb-2",children:"5x5 Grid Overlay"}),a&&e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-500/20 border border-blue-500"}),e.jsx("span",{children:"Grid Position"})]}),s&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500/30 border border-red-500"}),e.jsx("span",{children:"Reserved (Static)"})]})]})]})},Pr={x:0,y:0,scale:1,width:800,height:600},ce={min:.2,max:3},Me={boundary:2e3},Yr=({nodes:t,connections:r,mapType:a="connected",currentNodeId:s,onNodeClick:i,onNodeHover:n,onAccessNode:l,interactive:m=!0,showTooltips:c=!0,showAnimations:g=!0,showGridOverlay:h=!1,className:d,debugMode:b=!1})=>{const[x,w]=p.useState(Pr),[C,v]=p.useState(s||null),[o,A]=p.useState(null),[k,j]=p.useState(!1),[B,f]=p.useState({x:0,y:0}),E=p.useRef(null),T=p.useMemo(()=>{if(t.length===0)return[];const y=gr(t,a);return a==="isolated"&&y.length>0&&y.every(N=>N.position&&Number.isInteger(N.position.x)&&Number.isInteger(N.position.y)&&N.position.x>=O.MIN_COORD&&N.position.x<=O.MAX_COORD&&N.position.y>=O.MIN_COORD&&N.position.y<=O.MAX_COORD)?y.map(N=>({...N,position:_(N.position,x.width,x.height)})):hr(y,x.width,x.height,100)},[t,x.width,x.height,a]),L=p.useMemo(()=>{const y=xr(r,a),I=new Map(T.map(N=>[N.id,N.position]));return y.map(N=>({...N,fromPosition:I.get(N.fromNodeId)||{x:0,y:0},toPosition:I.get(N.toNodeId)||{x:0,y:0}}))},[r,T,a]);p.useEffect(()=>{const y=()=>{if(E.current){const I=E.current.getBoundingClientRect();w(N=>({...N,width:I.width,height:I.height}))}};return y(),window.addEventListener("resize",y),()=>window.removeEventListener("resize",y)},[]);const Q=p.useCallback(y=>{m&&(v(y),i&&i(y))},[m,i]),R=p.useCallback(y=>{m&&(A(y),n&&n(y))},[m,n]),V=p.useCallback(y=>{if(!m||y.button!==0)return;const I=E.current?.getBoundingClientRect();if(!I)return;const N={x:y.clientX-I.left,y:y.clientY-I.top};j(!0),f(N),y.preventDefault()},[m]),D=p.useCallback(y=>{if(!k||!m)return;const I=E.current?.getBoundingClientRect();if(!I)return;const N={x:y.clientX-I.left,y:y.clientY-I.top},K=N.x-B.x,H=N.y-B.y;w(z=>{const ne=(z.x+K)/z.scale,le=(z.y+H)/z.scale,be=Math.max(-2e3,Math.min(Me.boundary,ne)),fe=Math.max(-2e3,Math.min(Me.boundary,le)),st=be*z.scale,it=fe*z.scale;return{...z,x:st,y:it}}),f(N)},[k,m,B]),F=p.useCallback(()=>{j(!1)},[]),W=p.useCallback(y=>{if(!m)return;y.preventDefault();const I=E.current?.getBoundingClientRect();if(!I)return;const N=y.clientX-I.left,K=y.clientY-I.top,H=y.deltaY>0?.9:1.1;w(z=>{const ne=Math.max(ce.min,Math.min(ce.max,z.scale*H)),le=ne/z.scale,be=N-(N-z.x)*le,fe=K-(K-z.y)*le;return{...z,scale:ne,x:be,y:fe}})},[m]),se=p.useCallback(y=>{const I=T.find(N=>N.id===y);I&&w(N=>({...N,x:N.width/2-I.position.x*N.scale,y:N.height/2-I.position.y*N.scale}))},[T]),ie=p.useCallback(()=>{const y=Ze(T);if(y.width===0||y.height===0)return;const I=100,N=(x.width-2*I)/y.width,K=(x.height-2*I)/y.height,H=Math.min(N,K,1);w(z=>({...z,scale:H,x:(z.width-y.width*H)/2-y.minX*H,y:(z.height-y.height*H)/2-y.minY*H}))},[T,x.width,x.height]);p.useEffect(()=>{s&&s!==C&&(v(s),se(s))},[s,C,se]);const $={cursor:k?"grabbing":m?"grab":"default"},oe=`translate(${x.x}px, ${x.y}px) scale(${x.scale})`;return e.jsxs("div",{ref:E,style:$,className:u("relative w-full h-full overflow-hidden",!d?.includes("bg-transparent")&&"bg-linear-to-br from-slate-900 via-slate-800 to-slate-900",!d?.includes("border-0")&&"border border-slate-700","rounded-lg",m&&"select-none",d),onMouseDown:V,onMouseMove:D,onMouseUp:F,onMouseLeave:F,onWheel:W,children:[!d?.includes("bg-transparent")&&e.jsx("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:`
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,backgroundSize:`${50*x.scale}px ${50*x.scale}px`,backgroundPosition:`${x.x}px ${x.y}px`}}),h&&a==="isolated"&&e.jsx(Rr,{containerWidth:x.width,containerHeight:x.height,showCoordinates:b,showReservedPositions:b,opacity:.4}),e.jsxs("div",{className:"absolute inset-0",style:{transform:oe,transformOrigin:"0 0"},children:[e.jsx(Br,{connections:L,viewportWidth:x.width,viewportHeight:x.height,animated:g,debugMode:b}),e.jsx("div",{className:"relative",children:T.map(y=>e.jsx(Mr,{node:y,position:y.position,isSelected:C===y.id,showLabel:c,containerRef:E,viewport:x,debugMode:b,onClick:Q,onMouseEnter:R,onMouseLeave:()=>R(null),onAccessNode:l},y.id))})]}),e.jsx("div",{className:"absolute inset-0 pointer-events-none",style:{zIndex:1e3}}),b&&e.jsxs("div",{className:"absolute top-4 left-4 bg-black/80 text-white p-3 rounded-sm text-xs font-mono",children:[e.jsxs("div",{children:["Viewport: ",x.x.toFixed(0),", ",x.y.toFixed(0)]}),e.jsxs("div",{children:["Scale: ",x.scale.toFixed(2)]}),e.jsxs("div",{children:["Map Type: ",a]}),e.jsxs("div",{children:["Nodes: ",T.length]}),e.jsxs("div",{children:["Connections: ",L.length]}),e.jsxs("div",{children:["Selected: ",C||"None"]}),e.jsxs("div",{children:["Hovered: ",o||"None"]}),e.jsxs("div",{children:["Panning: ",k?"Yes":"No"]})]}),m&&e.jsxs("div",{className:"absolute bottom-4 right-4 flex flex-col gap-2",children:[e.jsx("button",{className:"px-3 py-2 bg-slate-800/90 text-white rounded-sm hover:bg-slate-700 text-xs",title:"Reset view",onClick:ie,children:"🔄"}),e.jsx("button",{className:"px-3 py-2 bg-slate-800/90 text-white rounded-sm hover:bg-slate-700 text-xs",title:"Zoom in",onClick:()=>w(y=>({...y,scale:Math.min(ce.max,y.scale*1.2)})),children:"➕"}),e.jsx("button",{className:"px-3 py-2 bg-slate-800/90 text-white rounded-sm hover:bg-slate-700 text-xs",title:"Zoom out",onClick:()=>w(y=>({...y,scale:Math.max(ce.min,y.scale*.8)})),children:"➖"})]})]})},Gr=(t,r)=>{const a=t.dialogue.rewards;typeof a=="number"&&a>0&&r.setQueryData(S.user.getCurrentUserInfo.key(),s=>{if(!s)return s;let i=s.cash;return t.dialogue.mugged?(i-=a,i=Math.max(0,i)):i+=a,{...s,cash:i}})},Qr=t=>{const r=Y(),a=J(),{setJustJailed:s}=Ue();return q(S.explore.interactWithNode.mutationOptions({onSuccess:i=>{if(!i.success)return;const{data:n}=i;if(n&&n.action==="character_encounter"){const l=n;r.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),Gr(l,r),l.dialogue.jailed&&s(!0)}else if(n&&n.action==="battle")r.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()}),a("/fight");else if(n&&n.action==="story"){const l=n;l.redirectToStoryPlayer&&l.episodeData?.id&&r.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})}else n&&n.action&&r.invalidateQueries({queryKey:S.explore.getMapByLocation.key()});t?.onSelectedNodeChange&&t.onSelectedNodeChange(null)},onError:i=>{console.error("Node interaction error:",i),t?.onSelectedNodeChange&&t.onSelectedNodeChange(null)}}))},$e=xe({base:"flex items-center justify-center h-96 rounded-2xl border",variants:{state:{loading:"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 border-blue-200/30 dark:border-gray-700",error:"bg-gradient-to-br from-red-50 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 border-red-200/50 dark:border-red-800/50"}}}),Fr=xe({base:"w-full p-3 rounded-lg border-2 transition-all duration-300 text-left cursor-pointer relative overflow-hidden group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 dark:focus-visible:ring-blue-400 dark:focus-visible:ring-offset-gray-800",variants:{status:{current:"bg-gradient-to-br from-amber-50 to-amber-100/50 dark:from-amber-900/20 dark:to-amber-800/10 border-amber-300 dark:border-amber-700",completed:"bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-300 dark:border-blue-700",available:"bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-700/50 border-gray-200 dark:border-gray-600",locked:"bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-700/50 border-gray-300 dark:border-gray-600 opacity-60 cursor-not-allowed"},interactive:{true:"hover:shadow-xl hover:shadow-emerald-500/20 dark:hover:shadow-emerald-400/10 hover:-translate-y-1 hover:border-emerald-400 dark:hover:border-emerald-500",false:""},nodeType:{BATTLE:"hover:border-red-400 hover:shadow-red-500/20 dark:hover:shadow-red-400/10",SHOP:"hover:border-green-400 hover:shadow-green-500/20 dark:hover:shadow-green-400/10",STORY:"hover:border-blue-400 hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10",CHARACTER_ENCOUNTER:"hover:border-teal-400 hover:shadow-teal-500/20 dark:hover:shadow-teal-400/10",MINING_NODE:"hover:border-stone-400 hover:shadow-stone-500/20 dark:hover:shadow-stone-400/10",SCAVENGE_NODE:"hover:border-emerald-400 hover:shadow-emerald-500/20 dark:hover:shadow-emerald-400/10",FORAGING_NODE:"hover:border-lime-400 hover:shadow-lime-500/20 dark:hover:shadow-lime-400/10",ACTION:"hover:border-orange-400 hover:shadow-orange-500/20 dark:hover:shadow-orange-400/10",CHOICE:"hover:border-yellow-400 hover:shadow-yellow-500/20 dark:hover:shadow-yellow-400/10",CONDITION:"hover:border-indigo-400 hover:shadow-indigo-500/20 dark:hover:shadow-indigo-400/10"}},compoundVariants:[{status:"available",interactive:!0,className:"hover:scale-[1.02]"}]}),Hr=xe({base:"w-full min-h-[600px] rounded-lg",variants:{background:{default:"bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800"}}}),Ur=xe({base:"relative before:absolute before:inset-0 before:opacity-30 before:pointer-events-none before:rounded-lg",variants:{nodeType:{BATTLE:"before:bg-gradient-to-br before:from-red-500/20 before:to-orange-500/10 dark:before:from-red-600/30 dark:before:to-orange-600/20",SHOP:"before:bg-gradient-to-br before:from-green-500/20 before:to-emerald-500/10 dark:before:from-green-600/30 dark:before:to-emerald-600/20",STORY:"before:bg-gradient-to-br before:from-blue-500/20 before:to-cyan-500/10 dark:before:from-blue-600/30 dark:before:to-cyan-600/20",CHARACTER_ENCOUNTER:"before:bg-gradient-to-br before:from-teal-500/20 before:to-cyan-500/10 dark:before:from-teal-600/30 dark:before:to-cyan-600/20",MINING_NODE:"before:bg-gradient-to-br before:from-stone-500/20 before:to-slate-500/10 dark:before:from-stone-600/30 dark:before:to-slate-600/20",SCAVENGE_NODE:"before:bg-gradient-to-br before:from-emerald-500/20 before:to-green-500/10 dark:before:from-emerald-600/30 dark:before:to-green-600/20",FORAGING_NODE:"before:bg-gradient-to-br before:from-lime-500/20 before:to-green-500/10 dark:before:from-lime-600/30 dark:before:to-green-600/20",ACTION:"before:bg-gradient-to-br before:from-orange-500/20 before:to-amber-500/10 dark:before:from-orange-600/30 dark:before:to-amber-600/20",CHOICE:"before:bg-gradient-to-br before:from-yellow-500/20 before:to-amber-500/10 dark:before:from-yellow-600/30 dark:before:to-amber-600/20",CONDITION:"before:bg-gradient-to-br before:from-indigo-500/20 before:to-purple-500/10 dark:before:from-indigo-600/30 dark:before:to-purple-600/20"}}}),Wr={"pink-600":"rgb(219, 39, 119)","purple-700":"rgb(126, 34, 206)","red-600":"rgb(220, 38, 38)","orange-700":"rgb(194, 65, 12)","blue-600":"rgb(37, 99, 235)","cyan-700":"rgb(14, 116, 144)","emerald-600":"rgb(5, 150, 105)","green-700":"rgb(21, 128, 61)","indigo-600":"rgb(79, 70, 229)","violet-700":"rgb(109, 40, 217)"},Jr=t=>t.replace("from-","").replace("to-","").split(" ").map(a=>Wr[a]||a).join(", "),qr=t=>{switch(t){case"STORY":return e.jsx(Yt,{className:"w-5 h-5"});case"BATTLE":return e.jsx(bt,{className:"w-5 h-5"});case"SHOP":return e.jsx(er,{className:"w-5 h-5"});case"CHARACTER_ENCOUNTER":return e.jsx(pt,{className:"w-5 h-5"});case"CHOICE":return e.jsx(Ee,{className:"w-5 h-5"});case"ACTION":return e.jsx(We,{className:"w-5 h-5"});case"CONDITION":return e.jsx(me,{className:"w-5 h-5"});case"MINING_NODE":return e.jsx(Dt,{className:"w-5 h-5"});case"SCAVENGE_NODE":return e.jsx(gt,{className:"w-5 h-5"});case"FORAGING_NODE":return e.jsx(Ht,{className:"w-5 h-5"});default:return e.jsx(Ee,{className:"w-5 h-5"})}},Vr=t=>{switch(t){case"STORY":return"from-amber-400 to-yellow-500";case"BATTLE":return"from-red-500 to-red-600";case"SHOP":return"from-green-500 to-green-600";case"CHARACTER_ENCOUNTER":return"from-teal-600 to-cyan-700";case"CHOICE":return"from-yellow-500 to-yellow-600";case"ACTION":return"from-orange-500 to-orange-600";case"CONDITION":return"from-indigo-500 to-indigo-600";case"MINING_NODE":return"from-stone-500 to-stone-600";case"SCAVENGE_NODE":return"from-emerald-500 to-emerald-600";case"FORAGING_NODE":return"from-lime-500 to-lime-600";default:return"from-gray-500 to-gray-600"}},Kr=t=>{switch(t){case"STORY":return{text:"Story",color:"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700"};case"BATTLE":return{text:"Battle",color:"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700"};case"SHOP":return{text:"Shop",color:"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700"};case"CHARACTER_ENCOUNTER":return{text:"Character",color:"bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300 border border-teal-200 dark:border-teal-700"};case"CHOICE":return{text:"Choice",color:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-700"};case"ACTION":return{text:"Action",color:"bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700"};case"CONDITION":return{text:"Condition",color:"bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700"};case"MINING_NODE":return{text:"Mining",color:"bg-stone-100 dark:bg-stone-900/30 text-stone-700 dark:text-stone-300 border border-stone-200 dark:border-stone-700"};case"SCAVENGE_NODE":return{text:"Scavenge",color:"bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-700"};case"FORAGING_NODE":return{text:"Foraging",color:"bg-lime-100 dark:bg-lime-900/30 text-lime-700 dark:text-lime-300 border border-lime-200 dark:border-lime-700"};default:return{text:"Unknown",color:"bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"}}},Xr=t=>{switch(t){case"available":return{text:"Available",color:"bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800"};case"locked":return{text:"Locked",color:"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700"};case"completed":return{text:"Completed",color:"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"};case"current":return{text:"Current",color:"bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800"};default:return{text:"Unknown",color:"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700"}}},_r=t=>{switch(t){case"BATTLE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M20 80L50 20L80 80Z" fill="%23ef4444" opacity="0.1"/%3E%3C/svg%3E')]`;case"SHOP":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Crect x="20" y="30" width="60" height="50" fill="%2322c55e" opacity="0.1"/%3E%3C/svg%3E')]`;case"STORY":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Crect x="25" y="20" width="50" height="60" fill="%233b82f6" opacity="0.1"/%3E%3C/svg%3E')]`;case"CHARACTER_ENCOUNTER":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Ccircle cx="50" cy="50" r="30" fill="%230d9488" opacity="0.1"/%3E%3C/svg%3E')]`;case"MINING_NODE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpolygon points="50,20 80,80 20,80" fill="%236b7280" opacity="0.1"/%3E%3C/svg%3E')]`;case"SCAVENGE_NODE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Ccircle cx="50" cy="50" r="25" fill="%2310b981" opacity="0.1"/%3E%3C/svg%3E')]`;case"FORAGING_NODE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M50 20Q60 30 70 50Q60 70 50 80Q40 70 30 50Q40 30 50 20Z" fill="%2384cc16" opacity="0.1"/%3E%3C/svg%3E')]`;case"ACTION":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M30 50L50 30L70 50L50 70Z" fill="%23f97316" opacity="0.1"/%3E%3C/svg%3E')]`;case"CHOICE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M25 40L50 20L75 40L50 60Z" fill="%23eab308" opacity="0.1"/%3E%3C/svg%3E')]`;case"CONDITION":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Crect x="30" y="30" width="40" height="40" fill="%236366f1" opacity="0.1"/%3E%3C/svg%3E')]`;default:return""}},Zr=t=>{switch(t){case"BATTLE":return{borderColor:"border-red-300 dark:border-red-700",shadowColor:"shadow-red-500/20 dark:shadow-red-400/10",hoverBorder:"hover:border-red-400",description:"Combat encounter awaits"};case"SHOP":return{borderColor:"border-green-300 dark:border-green-700",shadowColor:"shadow-green-500/20 dark:shadow-green-400/10",hoverBorder:"hover:border-green-400",description:"Merchant establishment"};case"STORY":return{borderColor:"border-amber-300 dark:border-yellow-600",shadowColor:"shadow-amber-500/30 dark:shadow-yellow-400/20",hoverBorder:"hover:border-amber-400",description:"Story episode awaits"};case"CHARACTER_ENCOUNTER":return{borderColor:"border-teal-300 dark:border-teal-700",shadowColor:"shadow-teal-500/20 dark:shadow-teal-400/10",hoverBorder:"hover:border-teal-400",description:"Meet interesting characters"};case"MINING_NODE":return{borderColor:"border-stone-300 dark:border-stone-700",shadowColor:"shadow-stone-500/20 dark:shadow-stone-400/10",hoverBorder:"hover:border-stone-400",description:"Extract valuable resources"};case"SCAVENGE_NODE":return{borderColor:"border-emerald-300 dark:border-emerald-700",shadowColor:"shadow-emerald-500/20 dark:shadow-emerald-400/10",hoverBorder:"hover:border-emerald-400",description:"Search for hidden items"};case"FORAGING_NODE":return{borderColor:"border-lime-300 dark:border-lime-700",shadowColor:"shadow-lime-500/20 dark:shadow-lime-400/10",hoverBorder:"hover:border-lime-400",description:"Gather natural materials"};default:return{borderColor:"border-gray-300 dark:border-gray-700",shadowColor:"shadow-gray-500/20 dark:shadow-gray-400/10",hoverBorder:"hover:border-gray-400",description:"Unknown location type"}}},$r=t=>{const r={available:0,current:1,completed:2,locked:3};return[...t].sort((a,s)=>r[a.status]-r[s.status])},ea={izumi:{positive:"/Izumi/happyclosed.webp",neutral:"/Izumi/happyopen.webp",sad:"/Izumi/sad.webp"},"???":{positive:"/Sakura/hidden.webp"},daiki:{positive:"/Daiki/happy.webp",neutral:"/Daiki/neutral.webp",sad:"/Daiki/sad.webp"},hana:{positive:"/Hana/happyopen.webp",neutral:"/Hana/shy.webp",sad:"/Hana/sad.webp"},hiroshi:{positive:"/Hiroshi/happyclosed.webp",neutral:"/Hiroshi/smug.webp",sad:"/Hiroshi/worried.webp"},kenzo:{positive:"/Kenzo/happy.webp",neutral:"/Kenzo/neutral.webp",sad:"/Kenzo/annoyed.webp"},mai:{positive:"/Mai/happyopen.webp",neutral:"/Mai/happyclosed.webp",sad:"/Mai/sad.webp"},tashiro:{positive:"/Tashiro/happyopen.webp",neutral:"/Tashiro/happyopen.webp",sad:"/Tashiro/worried.webp",negative:"/Tashiro/smug.webp"},apollo:{positive:"/Apollo/happyopen.webp"},yuta:{positive:"/Yuta/embarrassed.webp"},kazuya:{positive:"/Kazuya/neutral.webp"},katsuro:{positive:"/Katsuro/happyopen.webp"}},ze=(t,r)=>{const a="https://cloudflare-image.jamessut.workers.dev/static/characters";if(!t)return r?"":null;const s=t.toLowerCase();if(r)return s==="apollo"?"scale-[0.6] h-auto md:h-3/4 top-[55%] left-1/2 -translate-x-1/2 -translate-y-1/2":"scale-110 -left-[0.2rem] md:left-40 h-3/4 md:h-full md:-bottom-36 bottom-0";const i=ea[s],n=i?i.positive:null;return n?`${a}${n}`:null},ta=()=>{const t=Y();return q(S.explore.completeNode.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})}}))},ra=({dialogue:t,healed:r,nodeId:a,onClose:s})=>{const i=pe(),n=J(),{mutate:l}=ta(),m={hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},c={hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{duration:.75}}},g={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5}}},h={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.1}}};p.useEffect(()=>{i.start("visible")},[i]);const d=()=>{l({nodeId:a},{onSettled:()=>{s()}})},b=(w,C,v)=>typeof w!="number"||w===0?v?"You were caught littering! You were sent to jail.":`${C} went through your wallet but found nothing!`:w>0?v?`You were caught littering! You received a ¥${w} fine and were sent to jail.`:`${C} stole ¥${w} from your wallet!`:"…",x=t.isItemDrop;return e.jsxs(M.div,{className:"shadow md:rounded-b-lg",variants:m,initial:"hidden",animate:i,children:[e.jsx(M.div,{className:"wipe",variants:c,initial:"hidden",animate:"visible"}),e.jsxs("div",{className:"relative h-full overflow-hidden md:h-full md:rounded-lg",children:[e.jsx("img",{className:"h-full object-cover md:h-full md:scale-105 md:rounded-lg",src:ae(t?.location)||"",alt:""}),e.jsx("div",{className:"absolute bottom-0 size-full bg-black opacity-20 md:rounded-b-lg"}),e.jsx(M.div,{variants:h,initial:"hidden",animate:"visible",children:e.jsxs("div",{className:u("-translate-x-1/2 absolute left-1/2 z-50 w-[90%] skew-x-2 justify-end border-4 bg-slate-800 text-white opacity-95 shadow-lg md:h-44 md:skew-x-1",x?"-translate-y-1/2 top-1/2 border-blue-500 md:w-2/4":"bottom-14 md:w-3/4"),children:[!x&&e.jsx("div",{className:"-top-12 -translate-x-1/2 -rotate-3 -skew-x-2 md:-top-14 md:-skew-x-3 absolute left-[4.2rem] z-50 h-14 w-28 justify-end border-4 bg-slate-800 text-center text-white md:left-20 md:h-16 md:w-32",children:e.jsx("p",{className:"m-2 skew-x-2 text-2xl text-[#6dc7ff] md:skew-x-3 md:text-3xl",children:t?.character})}),e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"-skew-x-2 md:-skew-x-3 mx-6 my-4 flex h-[52%] flex-col md:mx-8 md:h-[55%] md:text-lg xl:text-xl 2xl:text-2xl",children:[x?e.jsx("p",{className:"mx-auto inline-block h-[70%]",children:"You found an item while exploring the zone!"}):e.jsx(e.Fragment,{children:t?.jailed?e.jsx("p",{className:"mx-auto inline-block h-[70%]",children:"Hey, stop right there!"}):e.jsx("p",{className:u(t?.line.length<16?"text-center":"text-left","inline-block h-[70%]"),children:t?.line})}),t?.mugged?e.jsxs("p",{className:"text-center text-red-500 md:mt-0",children:[b(t?.rewards,t?.character,t?.jailed),t?.hospitalised&&e.jsx("span",{children:" You were injured in the attack."})]}):e.jsx("div",{className:"text-center text-yellow-400 md:mt-0",children:r?e.jsx(e.Fragment,{children:" You were healed by the dog's presence "}):e.jsxs(e.Fragment,{children:[t?.isItemDrop?e.jsx("div",{className:"mt-1.5 flex",children:e.jsxs("span",{className:"mx-auto flex gap-2",children:["You gained 1x"," ",e.jsx(re,{item:t?.rewards,className:"inline-block h-6 md:h-8"})," ",e.jsx("span",{className:"text-sky-400 text-stroke-sm",children:t?.rewards?.name})]})}):e.jsx(p.Fragment,{children:typeof t?.rewards=="number"?`You gained ¥${t?.rewards}`:""}),t?.crateReward&&e.jsx("div",{className:"mt-1.5 flex",children:e.jsxs("span",{className:"mx-auto flex gap-1 text-sm md:text-base",children:["You found a",e.jsx(re,{item:t?.crateReward,className:"inline-block h-6 md:h-6"})," ",e.jsx("span",{className:"text-sky-400 text-sm text-stroke-sm md:text-base",children:t?.crateReward?.name}),e.jsx("span",{className:"hidden md:block",children:"in the trash"})]})})]})})]}),t?.jailed?e.jsx("button",{className:"-skew-x-2 md:-skew-x-3 mx-2 mb-[0.35rem] rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4",onClick:()=>{d(),n("/jail")},children:"Go to jail"}):e.jsx("button",{className:"-skew-x-2 md:-skew-x-3 mx-2 mb-[0.35rem] rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4",onClick:d,children:"Return to Explore"})]})]})}),!x&&e.jsx(M.div,{variants:g,initial:"hidden",animate:"visible",children:e.jsx("img",{src:ze(t?.character)||"",alt:"Encounter character",className:u(ze(t?.character,!0),"absolute z-0")})})]})]})},aa="/assets/arcade-BIkkTRDG.png",sa="data:image/webp;base64,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",ia="data:image/webp;base64,UklGRpAFAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSEQBAAABkGtr27FHT2zWSWWnsmujsu0cA6p0tpO/Ts7ATipjHNvGexe/gucbz6yImAD6P1B32S7Y3JW5vlD8PVi9i3sRt3swe+f6EjKw2/sSe/wc+BurJ/gB7prM1AHPi5ZSgFZJOJME/CeekIQ9SZBJguuTFFC6kALyXn5i6Eot5Rope4wsvYRORQh5ToLT6pfwwN2cAKePNi9BNeBVZNPL5h4IPsRGKL1f4BHE/q+Ip18Oz4xF7q76qOazthvJz8789IGnKp4Hswvb/GwuYXrPQ06AyGNvCkvf+InZ3cbUjpMip51RHO/E8kMpO6cY23GQs9kawd3XMuK4cOMaY1/lvg5BfKgknsu2HjEkNwis9mgwReXfgEFgEPgwoEVsV21A4caUIfGt0bght79iQZxrdOwBF9vWxLt2//H1oSNxr5VSakU/swFWUDggJgQAANAYAJ0BKmQAZAA+kTyZSCWjoqEttJwAsBIJYwDUDONBqPz6CNu5zwfnwdL36rnoV9MV/nsFA/u78nWBPF+EDSWY3f8l9wHyE57fqX0Zuqqj56qFIs/2so66pGuOSZzKrcOaloUPlRsAZyB7pxMc4ACSb7CzWFqjoiGSdToPxpkEdb9IiwsT4wxK3fssClmw2NDXYNJ0/MNKIhQKSjTbZHVuddhgq1dVJcSCdM6xMQssHcZMTJ/KBG0J5NwVEvZBnYa373y1hZY2oGnAinXKAAD+/FzQDO0d3lcV0/F/2In7/kxYf9ul2szT7KYK/x5AUvagzmFHjHKw51YgDOa8GHaceCVmYtLaWzUvxakmqCMbqPf4tV4P+hke2Juy8uOKUHTcsgH0nniH4xTVr3Zndawh+ObmJTHz6fYbEwbXcfrVGpxREu4x15w1MX5iv840NQtrr3EoRe0OpfcE8kcakSpm5hvwd34ckV9s/hnwicjjRtuOTG+brMk24UzvQROB6tk8YvsLMsUJpwBWboYYcqk+HBtI6QWN0AP5TBk1G4+fsGWwv2LKWmekonIJ3fpBkLmU5zax//wnyM84vXwM6CVBHCn/TENctaHD5BZDzc85oleRkAY3voUG/Rtx7srDuwlJRjQY7xRuSgPLPhJStfXokxqXRlYwlO0M/Lnk/B3U+QlhyvsbKGhfgIt+cldbf4wHTB+CZAGKrgawgPpXFuVPkxtNoEa/W8eOYiA9ZXe2qzGHZCY5p0qpA+VhiyfXwsb8AM8dMaSkW5TWX4CHPFs+O5VKNbq3avLWwjWzefp/mPco+jU0wqVDmlHO9icuQL+u1W3/8sfqSCDAJx6C2avPmMNjIze8kcxr6+YQ9MQcLmmsxdP3462U11v+A9oihm0c1cWx8umNlfy5skDQ3s5JPmzDgKQYU5Yd3074cNM0r0bylApoSlJrDyAgTbaTUt8eAJy8wa8/sfec0iyjfxv0QKH3JhKuL2NTTiCWbdrwDLM/a84Pjr3836UKyAJ+M0MP/uxeBwG5z0rDHYt/vrUpN9Axb7rzLzWXacJDbhqUi8P249FSvKF9tYU/IJ4ewj8RHN8a4OdY+gNoUkNzahq+tOjIgY8dIY0V4uv19y/6/SUv2NZdsdFBHPBXonGxERIS6GldlHHsbD52hJ1UqdenTjjtdAHaBWPtk+U2QLodknZs51KH7a5QKPv+yVCs8/YBJenI4mvywPrGStybABba+Q/IIqJ7WcWZ5a+UM3q+9/P8aH1uuPoBie2ZUuyaDA+4n4dUk/FOy7uSKkYCsFvBz7VfY+Blxw/bYpoVq9Atp2etFKBCH0yq30LUo10SqpJD+YCPAzRjU6NkLR/EYLR7GOnPS1HwWJqWWr2WNoBjf+tZXRRzYAm+rI/yHgaTH+orMAAAAA==",oa="/assets/casino-esO0707x.webp",na="data:image/webp;base64,UklGRgoFAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSJMBAAABkGtr27FHT6yvjJPSrt3ZVuXOqe3kDMb2HIBd2p7pYtvJ0w3u+38zLCJiAuQ/nbGtV17Ob+s3b8+/uNIaY0Ostrf6w9+2WrbBsWVOf+pss6MNCHmmP/1pMF32igIuZ5FV7ijkdgVV9o6CbmcQBS0r7EoojeMzBX7swNKi0I0k1hzWjAdHm4K3cLxFe00Rq/CRDG14zQxX8C4xvMJ7zjCLNwOXcvSD4m+ChTxQTqzkebV9IfNqgAdqgBQ1wVEjfDDCihF2jLBihGmebaSPPItId3gmkE7zfEVq53mNlM3zCCmQ5x6SfKU5D3WIZgAqj6YeymWeJQVKjrAEY8WQrDlhyS2OuwIet0/RiyZXKdLgApYI1iw4qSI4J4TH8NIYnK6hfXZgEOsOWIlwupyCum9HIlI5gTPuL7xWw0OUIOFGkQOP8wiKF9Wwoi4VMo3B6F4hkQIveplAe4zwzAjLRtA/DktGeGqEbhMsevKM4ezlC+8QzGKBEDsPoXgKN4ocgMYwRtmGMAbZnIfGft7ooDPbfxQBAFZQOCBQAwAAMBMAnQEqZABkAD6RQJlJJaQioSsXisCwEglAGoiD5a8LrgS7YrxJfWG9De8degB0sVeH6Du9mUr9E8wNLjMs/Uv0Q6ahn59XEumr1sxZhOWAhDts9lyKz37gBqdwvgL2obSgWEaEQqmD6aCTT5HrYVlSAUynVxIuxR/WlKktuvE42w1lYlKAh4nEkYOP0mqWXStgHNZsiJida/vPsbV/KzgAAP78XNX3M5elK6I9dCpPPmvf35Fp7LIG1rXrCQgQGqzQVWj5O8RMyYH36+WA8Ei06xmzewbKs1VDCA/CTcFl67+MEOR2y+WZQqSfFrwbhZx565+FstfxtPhNLsBNgcf5abwZYZ1MftAcYaJYpvaPLbJcDBmw34CVsFke13Aa3J332lt3WUvTxqCPHhvXWIuU0n8QGTisNs3Hmq28y9LkguCPV/SESDAmu0HLwNZi/bGG7nz7LiGMuPKKm/2mJ5/HzNLMIvmQNqdcKZOZs+Z9EBFhURq9RJpXeCeVFU0dqHXUQhkBGcfNqFbFCJsoElAE7cGP5O5DmOl/Orn6jw39hpIcN8GO6MRpQoCQ30mv9Ji/U1nFJKKV6Y9bG42IisRMKdvvJUIH7IR+yLv/nkUBWH1HctPWOo+pCzOvwNDR3Z/UYd+v2t1patNjKxpJTuXsB+mO8I6heY6oyhs298b1Y1AjhuRPnSS4yrCZzF845EjNOXdt59CTVJJjhF4JoA0zrdsmMrKyLHBEj7gpu6ex/xA29Y4HlinZzOgF69u6bw+J0a1Mg1cK1wH7OO+fDPZxZDjzZ3yzcXFwMYSPaDf54FkQNF8rAi3Q78LD2boSpjK7JtPXu5/6NzF6OZL7I3l1FN989QxsvUhBJebG/DFhYkeSblen+E8yI3mvoqOEGs8NVSROOp8xMz/qwMxcDViIcLtJoR97ecKD5PsNBOuOF+u/LGBV9/yQ08rqH10RdmWMSi4MfYk/okCsCn2PRsmmTc5NBzp/n3w9cie5D6P9w/8GBoLZngSrG++o8rVmZibzTeNtp/wzUtlfD/wl8ycYhHZKh1RQpO3g2dODoX/H/OxQpdc/lDrCbQgGi++/s5Q0hz37A818XiREUQLlW8ekRY+WfQ4f4n3KAAAAAAA=",la="data:image/webp;base64,UklGRpAFAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSL8BAAABkGNr2xo3vx3QScfMlReQjpk2kCpVakMVxgXgKlIzk/AMhZnZzNaYLT0BzS/p1x9OERETIP7j6RHtao3K/xp4JO9qhDT/gnJSsNUy6gvlcq52YkKSEiKT8x8QeW+exowHvMn7fq6Re4a+GmjsBuhqpE5fRXJE5sjry8eP6qNPX0j/GjkV7Mz1vb52cseipLItF/3saI3W2loe2dZ7CC9trdLXW75/Y74Dd6m27kdAUHjF6JHxmrIrwJg5wPXJWTDqC+VyrnaiGvD2LY9nqjf/AZH35qXwSoru+9ybrJrxgDd53881cs9ILnwrxcATLlUr1kBjN0BXI3XJcUuOzs/sFUJ4pB82unvnCiGK5IjMkU+hcViOh6PDNUKg6NiB8cLHj+qjL7kSVgxMLqsD1iSkk9tEW2eMnk5WKcRZNcQ5cjEwOaNSzBRmt2HG6BoIlmhHrB8OHTkcdio0oorYHgRX5QrkFQqVEduG8bplykOhQjHTESs+0eIFlbiLlsT0U/DMG6lgakqI9Q60WndGvnO1JcSy0wH0lG7lnt3Wjl7/tunSQXt6uv3b5qEOHqan2+RE5v9tcmTs7Dkyttz/VAEAVlA4IKoDAADQGACdASpkAGQAPpE8mEiloyKhLRQMaLASCUAanv0Pmb7e3b5c8d/6Ae6jbUeYDzoP7l+s3ur3g30IPLc9nrAHf7h2sf4Wu47/e8GUEYp9GnSW8LUwZ/n+dF8AkPZlP3egFNWU9UMSpbGNj4GCdILar4Oe2gaVcml7RUp1DKJEepPKV7uTU5FOV9Oc/gx3DiFlIO1mrE58cf8wU9lZd56O95PQsUvhP6aNjUSBPV4fkRi5omA+yk7tAv2iHK2uef95wAC+81+3r/2RYgSB/AAA/vxc1Gpw+6dNblIBgYG9eVHgDYUN0Zj3Al+lf3wfRsSLwDFr4G3BtP5dwGKFUGe3RuAHWakXqihV7b0Ga4MDxGY3M0t5lJ97T7Z4i4EyElPFbs7HRQQmk/2Cwnq6HEcCesZVPf0hVyyLxAmNLn9XwrEgIVvM8iDYq/xu8w9Nk4q+XqxvkG9kCBQC4YeBsw2/tTX7u9B4KDsf1uQ4+YqMy0UZ03nuX8XhOOOdIDY/dF//exBAFc/f3/2I/YXluDL7OT10zX/60mWuOhbYBwFna2zt7OxD/ho48LlMINUumtkGjCzx5LdVcP1knVzxGsJs+GiH3mJndx/ARmxwb2+Wf/z13+8iwsZ8flLCcqAE/Y9uwfWvpRgE/ZWTaY4ROjSN1PjtFZgIoUaiQC8H9S/ZPo+32nZLjcnrnK9HsNmL2y/xkqSHodUKlUOA94djd2tB9WyTNo02M6xjqAsuJ5u7jJWtBDeLa/XW2CKLU8cnJhcYHSz7zQUhUZ+ao6EYqu6mfxIH4twff4bAIGO7u2G+kqivKOVtIpG8zoMbb7HYd9kjp5ti+31t+KmWyGbtze6j+0jMDPunShyctHSXcG7c5RBwfLmXaisDk5+nvc9oTrJVhV2YzTHm9bt1s4LDhCDaohI9/0hj6M+NptP5PB45hKGOGk/UESnLQUL+zf1K6k5H32Sz9ekXBUe8ebw73AlfQ5WYueq3Jme4XWPVbkBbCKp0Ue4rDcZ7cztsH0xJCGzF/mr5VJ/S/bFKdmn6sOEuyeaXa3TcgTel6E0nRfj8WgcysZ4gabZdGj0WcKp6DA5IdvZOgEcGAG7c1/en5PQeGCO9JccqSDjGEALJfGcdC0MfDR8i5FQbvmcOiQ4S2fNufhWebL8UvkZR0UwxF/4T93dOO18KaZ9Gv7w7V//7SO6TRO96w5g1Rngc43jwu5GU/Km3vjfhVvf8hooBgpDzqWgT+iVXVkAAAA==",ca="data:image/webp;base64,UklGRtIDAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSJIAAAABYBzbdpKflXWOS4/JjhSAu0RwmIhg5LZtJPradvYZY54Ar5gQXzSKIzysXtpoSKwckjpIpBprtIRdT8XCSk3TFHP7oh2MsnkP/mrkk5Xm96BZsS9noQMXK/ruqjZs9JAHhTHv4nVv5RPyX2Dju7gwa8jWu+5bbWOvy+3odRcTkjpkSDVIdIhAOeJx1biD4Q0TAFZQOCAaAwAA8BIAnQEqZABkAD6JOpVHpSOiITOYCoigEQlnEuA8QPR5+HCB6P0bugIDJ74ryBXiquwB3CPkQ/zPpWaJ/IWqNPr01vNA26ucgHdHQfbDrOPLpL0JwBBKPj5UYLps5v1EHtAZ0EntvwNERxxIsN7X0J4tatFwedUwdxyZeGtYJDfoZCstzDr015CvlTUzz+/HobQMU85VKSoiq5jj3x/iAAD++0AjZlL8Ptq4JGaslk3LEW3PvEiVh1VF3xjHJ9hCrPQ0Zca33JsM0c1IQ/e8yjpBdUbJffLI20dLW/jL36+nhLB/4g6vtTLiCy3m0UoeatbQzfnckgzYZX8LoZlIWNbosOkt//2hD1DQKqkHrJHhHexbLIdu+GB4Y1nfs+LLvu/wfFu/5iRSZwi9ivP7epG1FjiH0crfog3w2rzbVO34tDdIKA+sP+NSGP3vfkhu3v7Kiu5HK8xZ6CQhYpgV3BJbeKNALW9E6vR4YjPBWKnUs1Zg9x2KurnBMqgEePLW210ugVSNUmHX8UhemUX2fifyjs5zoWcXtUKtecfe+DqxmPp78mQZwLNEGnFSbROFuXKu0rLTj4SLqOjPvXnPGRk2FyC4Dzaw48E9B7QczqmuofFFYWrf+Bxam+xeb6KYHBq3Ae+TdD+pmUKqwQKILnnEy5Ma91tHpf02irzrtu0G8889E4mlofI+la6XHTwW8NRjiS+92Tv48xdvyGX3paidmG+x+UvU+Fzp/PnmHzOBG85xr8w+FwCbk4c0GUBQmmQznr3vefN00X1f6RQBkMfmc6UiPecIyoFvehVnvJWvKuk/WRQFU3wNO4QACmS7rpbjE8Fwm8/nVXjYNfY9QzGAvc7hSTZ+myHe6Ym+y8xWfIHm/8m02ILSHzsuA7y7/7cfZyv5nr0TWiRUPrarwoC5IftMyM9rLtqrDYsVR7pF9+Jwqspi8wU06p+tbtgGKmiiQMFkVOOE4UV/WzpYdzF+AK3ujvtH87Tn4B18adt+NjNHjBLWmVa6MMv5EG3kzyL8UTf9Vf/GOVZNN6nS3e0K5rQwAAAAAAA=",da="/assets/jobs-BDLp4JM9.webp",ua="/assets/shopsicon-CxJUSSkR.webp",ma="/assets/shrineicon-BvwFPa2C.webp",ha=[{id:"training",name:"Training",description:"Improve your combat stats and grow stronger",icon:na,route:"/training",color:"bg-yellow-500 dark:bg-yellow-600"},{id:"shops",name:"Shops",description:"Buy and sell items",icon:ua,route:"/shops",color:"bg-green-500 dark:bg-green-600"},{id:"bank",name:"Bank",description:"Manage your finances and investments",icon:ia,route:"/bank",color:"bg-teal-500 dark:bg-teal-600"},{id:"shrine",name:"Shrine",description:"Donate to the shrine for blessings",icon:ma,route:"/shrine",color:"bg-amber-500 dark:bg-amber-600"},{id:"hospital",name:"Hospital",description:"Recover from injuries and restore health",icon:la,route:"/hospital",color:"bg-pink-500 dark:bg-pink-600"},{id:"jail",name:"Jail",description:"Serve time or visit incarcerated players",icon:ca,route:"/jail",color:"bg-slate-600 dark:bg-slate-700"},{id:"casino",name:"Casino",description:"Try your luck with games of chance",icon:oa,route:"/casino",color:"bg-purple-500 dark:bg-purple-600"},{id:"job",name:"Part-Time Job",description:"Work part-time to earn money and experience",icon:da,route:"/job",color:"bg-red-500 dark:bg-red-600",gateKey:"job"},{id:"market",name:"Market",description:"Trade items in the player marketplace",icon:sa,route:"/market",color:"bg-blue-500 dark:bg-blue-600",gateKey:"market"},{id:"arcade",name:"Arcade",description:"Play mini-games",icon:aa,route:"/arcade",color:"bg-purple-500 dark:bg-purple-600",gateKey:"arcade"}],De=()=>{const t=J(),{data:r}=ke(),a=s=>{t(s.route)};return e.jsxs("div",{className:"mb-4 lg:mb-0",children:[e.jsxs("div",{className:"mb-3",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Common Locations"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Available in every district"})]}),e.jsx("div",{className:"grid grid-cols-5 sm:grid-cols-3 lg:grid-cols-1 gap-2 lg:gap-3",children:ha.map(s=>{const i=s.gateKey&&r?.level?ft(s.gateKey,r?.level):null,n=i?.isLocked,l=n?i.message:s.description;return e.jsx("button",{disabled:n,className:u("cursor-pointer p-2 lg:p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg transition-all duration-200 hover:shadow-md group",n?"opacity-60 cursor-not-allowed hover:bg-white dark:hover:bg-gray-800 hover:border-gray-200 dark:hover:border-gray-700":"hover:bg-gray-50 dark:hover:bg-gray-750 hover:border-gray-300 dark:hover:border-gray-600"),onClick:()=>a(s),children:e.jsxs("div",{className:"flex flex-col lg:flex-row items-center lg:items-center text-center lg:text-left space-y-1 lg:space-y-0 lg:space-x-3",children:[e.jsx("div",{className:u("w-8 h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center text-white transition-transform duration-200 flex-shrink-0",s.color,!n&&"group-hover:scale-110"),children:e.jsx("img",{src:s.icon,alt:"",className:"hidden size-full md:block"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-xs lg:text-sm font-medium text-gray-900 dark:text-white line-clamp-1",children:s.name}),e.jsx("p",{className:u("text-xs text-gray-600 dark:text-gray-400 line-clamp-1 lg:line-clamp-2 mt-0.5 lg:mt-1 hidden sm:block lg:block",n&&"text-amber-600 dark:text-amber-400"),children:l})]})]})},s.id)})})]})},xa=()=>e.jsx("div",{className:$e({state:"error"}),children:e.jsxs("div",{className:"flex flex-col items-center space-y-4 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center",children:e.jsx(Ke,{className:"w-8 h-8 text-red-600 dark:text-red-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-red-700 dark:text-red-300",children:"Failed to Load"}),e.jsx("p",{className:"text-sm text-red-600 dark:text-red-400 mt-1",children:"Unable to fetch exploration areas"})]})]})}),ga=()=>e.jsx("div",{className:$e({state:"loading"}),children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"w-12 h-12 animate-spin text-blue-500"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-pulse"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-lg font-semibold text-gray-700 dark:text-gray-200",children:"Loading District"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"Discovering new exploration areas..."})]})]})}),pa=()=>{const t=Y();return q(S.explore.processForagingOperation.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.user.getInventory.key()})}}))},ye={container:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},wipe:{hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{ease:"easeInOut",duration:.75}}},dialogueBox:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.3,ease:"easeOut"}}}},Le={herbs:"https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",mushrooms:"https://img.icons8.com/?size=100&id=43138&format=png",berries:"https://img.icons8.com/?size=100&id=bqO7Szu38UJF&format=png",flowers:"https://img.icons8.com/?size=100&id=d6K0u3dO9c4C&format=png",roots:"https://img.icons8.com/?size=100&id=C3GJQJl8Dqjp&format=png",medicinal:"https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png",plants:"https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png"},et={easy:{color:"text-green-400",bgColor:"bg-green-500/20 border-green-400/30",icon:"🌱",label:"Easy",description:"Gentle gathering, common finds"},medium:{color:"text-yellow-400",bgColor:"bg-yellow-500/20 border-yellow-400/30",icon:"🌿",label:"Medium",description:"Careful foraging, rarer plants"},hard:{color:"text-red-400",bgColor:"bg-red-500/20 border-red-400/30",icon:"🍄",label:"Hard",description:"Dangerous terrain, exotic species"}},ba=t=>Le[t]||Le.herbs,fa=()=>{const t=Y();return{invalidateQueries:async()=>{await Promise.all([t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})])}}},ya=({isSuccess:t})=>{const r=t?"text-green-400":"text-red-400",a=t?"bg-green-500/20":"bg-red-500/20";return e.jsx("div",{className:u("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center",a),children:e.jsx("svg",{className:u("w-8 h-8 md:w-10 md:h-10",r),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})},va=({item:t,quantity:r})=>e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 md:gap-4",children:[e.jsxs("span",{className:"text-white text-lg md:text-xl font-semibold",children:[r??1,"x"]}),e.jsx(re,{item:t,height:"h-10 w-auto md:h-12 lg:h-14",className:"inline-block"}),e.jsx("span",{className:u("text-lg md:text-xl font-semibold",Ie(t.rarity)),children:t.name})]})}),wa=({foragingType:t,difficulty:r,onStartForaging:a,isDisabled:s})=>{const i=et[r];return e.jsxs("button",{type:"button",disabled:s,className:u("cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-600 to-emerald-700","hover:from-green-500 hover:to-emerald-600 transition-all duration-300","border-2 border-green-400/30 hover:border-green-400/60","shadow-lg hover:shadow-xl transform hover:scale-[1.02]","disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none","p-6 md:p-8 w-full max-w-md mx-auto"),onClick:a,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"relative flex flex-col items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:ba(t),className:"w-12 h-12 md:w-16 md:h-16 object-contain",alt:`${t} foraging`}),e.jsxs("div",{className:"text-left",children:[e.jsxs("h3",{className:"text-white font-bold text-lg md:text-xl capitalize",children:["Gather ",t]}),e.jsxs("div",{className:u("text-sm font-medium",i.color),children:[i.icon," ",i.label]})]})]}),e.jsx("p",{className:"text-gray-200 text-sm text-center",children:i.description})]})]})},ja=({foragingResult:t,onAction:r,isLoading:a,isMobile:s,actionButtonText:i})=>e.jsxs("div",{className:"flex flex-col items-center gap-6 md:gap-8 text-center",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(ya,{isSuccess:t.success}),e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl",children:t.message})]}),t.success&&t.itemReward&&e.jsx(va,{item:t.itemReward,quantity:t.itemQuantity}),!t.success&&t.injury&&e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30",children:e.jsx(Ce,{currentEffects:[{id:1,count:1,debuff:t.injury}],className:"justify-center"})}),e.jsx("div",{className:"w-full max-w-sm",children:e.jsx(ge,{variant:"primary",size:s?"md":"lg",isLoading:a,className:"w-full text-base md:text-lg",onClick:r,children:i})})]}),Na=({nodeId:t,foragingType:r,difficulty:a,onClose:s})=>{const i=pe(),[n,l]=p.useState(!1),[m,c]=p.useState(null),g=he(),{invalidateQueries:h}=fa(),{mutate:d,isPending:b}=pa(),x=ae("citystreet1");p.useEffect(()=>{i.start("visible")},[i]);const w=()=>{b||d({nodeId:t},{onSuccess:o=>{if(!o){c({success:!1,message:"No response received from server"});return}c({success:o.success||!1,message:o.message||"Foraging operation completed",itemReward:o.itemReward,itemQuantity:o.itemQuantity,injury:o.injury})},onError:o=>{c({success:!1,message:o?.message||"Something went wrong during foraging"})}})},C=async()=>{l(!0),await h(),s(),l(!1)},v=et[a];return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",children:e.jsxs(M.div,{className:"relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",variants:ye.container,initial:"hidden",animate:i,children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{className:"w-full h-full object-cover",src:x||"",alt:"Foraging location"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50"})]}),e.jsx(M.div,{className:"absolute inset-0 bg-black z-10",variants:ye.wipe,initial:"hidden",animate:"visible"}),e.jsx(M.div,{variants:ye.dialogueBox,initial:"hidden",animate:"visible",className:"relative z-20 h-full flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-green-500/80 rounded-2xl shadow-2xl",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-green-500/30",children:[e.jsx("h2",{className:"text-center text-green-400 text-xl md:text-2xl lg:text-3xl font-semibold",children:"Foraging Expedition"}),e.jsx("div",{className:u("text-center mt-2 px-3 py-1 rounded-full inline-block",v.bgColor),children:e.jsxs("span",{className:u("text-sm font-medium",v.color),children:[v.icon," ",v.label," Difficulty"]})})]}),e.jsx("div",{className:"p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col",children:m?e.jsx("div",{className:"flex-1 flex flex-col justify-center",children:e.jsx(ja,{foragingResult:m,isLoading:n,isMobile:g,actionButtonText:"Return to Explore",onAction:C})}):e.jsxs("div",{className:"flex-1 flex flex-col justify-center gap-6 md:gap-8",children:[e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto",children:["You've found a promising foraging site rich with"," ",e.jsx("span",{className:"text-green-400 font-semibold capitalize",children:r}),". Careful searching may yield valuable natural materials, but watch out for hazards."]})}),b?e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-gray-300 text-base md:text-lg",children:[e.jsx("div",{className:"size-6 border-2 border-green-400 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Foraging in progress..."})]})}):e.jsx(wa,{foragingType:r,difficulty:a,isDisabled:b,onStartForaging:w})]})})]})})]})})},ka=()=>{const t=Y();return q(S.explore.processMiningOperation.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.user.getInventory.key()})}}))},ve={container:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},wipe:{hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{ease:"easeInOut",duration:.75}}},dialogueBox:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.3,ease:"easeOut"}}}},Be={coal:"https://img.icons8.com/?size=100&id=1437&format=png",copper:"https://img.icons8.com/?size=100&id=qf6FQ7o8i8DI&format=png",iron:"https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png",gold:"https://img.icons8.com/?size=100&id=43138&format=png",crystal:"https://img.icons8.com/?size=100&id=fDyZg5u1hAeF&format=png",gems:"https://img.icons8.com/?size=100&id=11234&format=png",ore:"https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png"},tt={easy:{color:"text-green-400",bgColor:"bg-green-500/20 border-green-400/30",icon:"⚡",label:"Easy",description:"Low risk, steady rewards"},medium:{color:"text-yellow-400",bgColor:"bg-yellow-500/20 border-yellow-400/30",icon:"⚠️",label:"Medium",description:"Moderate risk, better rewards"},hard:{color:"text-red-400",bgColor:"bg-red-500/20 border-red-400/30",icon:"💀",label:"Hard",description:"High risk, valuable rewards"}},Ca=t=>Be[t]||Be.ore,Aa=()=>{const t=Y();return{invalidateQueries:async()=>{await Promise.all([t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})])}}},Ia=({isSuccess:t})=>{const r=t?"text-green-400":"text-red-400",a=t?"bg-green-500/20":"bg-red-500/20";return e.jsx("div",{className:u("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center",a),children:e.jsx("svg",{className:u("w-8 h-8 md:w-10 md:h-10",r),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})},Sa=({item:t,quantity:r})=>e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 md:gap-4",children:[e.jsxs("span",{className:"text-white text-lg md:text-xl font-semibold",children:[r??1,"x"]}),e.jsx(re,{item:t,height:"h-10 w-auto md:h-12 lg:h-14",className:"inline-block"}),e.jsx("span",{className:u("text-lg md:text-xl font-semibold",Ie(t.rarity)),children:t.name})]})}),Ta=({miningType:t,difficulty:r,onStartMining:a,isDisabled:s})=>{const i=tt[r];return e.jsxs("button",{type:"button",disabled:s,className:u("cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-amber-600 to-orange-700","hover:from-amber-500 hover:to-orange-600 transition-all duration-300","border-2 border-amber-400/30 hover:border-amber-400/60","shadow-lg hover:shadow-xl transform hover:scale-[1.02]","disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none","p-6 md:p-8 w-full max-w-md mx-auto"),onClick:a,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"relative flex flex-col items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:Ca(t),className:"w-12 h-12 md:w-16 md:h-16 object-contain",alt:`${t} mining`}),e.jsxs("div",{className:"text-left",children:[e.jsxs("h3",{className:"text-white font-bold text-lg md:text-xl capitalize",children:["Mine ",t]}),e.jsxs("div",{className:u("text-sm font-medium",i.color),children:[i.icon," ",i.label]})]})]}),e.jsx("p",{className:"text-gray-200 text-sm text-center",children:i.description})]})]})},Re=({miningResult:t,onAction:r,isLoading:a,isMobile:s,actionButtonText:i,actionButtonVariant:n="primary"})=>e.jsxs("div",{className:"flex flex-col items-center gap-6 md:gap-8 text-center",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(Ia,{isSuccess:t.success}),e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl",children:t.message})]}),t.success&&t.itemReward&&e.jsx(Sa,{item:t.itemReward,quantity:t.itemQuantity}),!t.success&&t.injury&&e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30",children:e.jsx(Ce,{currentEffects:[{id:1,count:1,debuff:t.injury}],className:"justify-center"})}),e.jsx("div",{className:"w-full max-w-sm",children:e.jsx(ge,{variant:n,size:s?"md":"lg",isLoading:a,className:"w-full text-base md:text-lg",onClick:r,children:i})})]}),Ea=({nodeId:t,miningType:r,difficulty:a,onClose:s})=>{const i=pe(),[n,l]=p.useState(!1),[m,c]=p.useState(null),g=he(),h=J(),{invalidateQueries:d}=Aa(),{mutate:b,isPending:x}=ka(),w=ae("citystreet1");p.useEffect(()=>{i.start("visible")},[i]);const C=()=>{x||b({nodeId:t},{onSuccess:j=>{c({success:j.success||!1,message:j.message||"Mining operation completed",itemReward:j.itemReward,itemQuantity:j.itemQuantity,injury:j.injury})},onError:j=>{c({success:!1,message:j.message||"Something went wrong during mining"})}})},v=async()=>{l(!0),await d(),s(),l(!1)},o=async()=>{l(!0),await d(),h("/jail")},A=()=>m?m.jailed?e.jsx(Re,{miningResult:m,isLoading:n,isMobile:g,actionButtonText:"Go to Jail",actionButtonVariant:"destructive",onAction:o}):e.jsx(Re,{miningResult:m,isLoading:n,isMobile:g,actionButtonText:"Return to Explore",onAction:v}):null,k=tt[a];return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",children:e.jsxs(M.div,{className:"relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",variants:ve.container,initial:"hidden",animate:i,children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{className:"w-full h-full object-cover",src:w||"",alt:"Mining location"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50"})]}),e.jsx(M.div,{className:"absolute inset-0 bg-black z-10",variants:ve.wipe,initial:"hidden",animate:"visible"}),e.jsx(M.div,{variants:ve.dialogueBox,initial:"hidden",animate:"visible",className:"relative z-20 h-full flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-amber-500/80 rounded-2xl shadow-2xl",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-amber-500/30",children:[e.jsx("h2",{className:"text-center text-amber-400 text-xl md:text-2xl lg:text-3xl font-semibold",children:"Mining Operation"}),e.jsx("div",{className:u("text-center mt-2 px-3 py-1 rounded-full inline-block",k.bgColor),children:e.jsxs("span",{className:u("text-sm font-medium",k.color),children:[k.icon," ",k.label," Difficulty"]})})]}),e.jsx("div",{className:"p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col",children:m?e.jsx("div",{className:"flex-1 flex flex-col justify-center",children:A()}):e.jsxs("div",{className:"flex-1 flex flex-col justify-center gap-6 md:gap-8",children:[e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto",children:["You've discovered a rich mining site with"," ",e.jsx("span",{className:"text-amber-400 font-semibold capitalize",children:r})," ","deposits. The mining operation will consume energy and carries risks, but may yield valuable materials."]})}),x?e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-gray-300 text-base md:text-lg",children:[e.jsx("div",{className:"size-6 border-2 border-amber-400 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Mining in progress..."})]})}):e.jsx(Ta,{miningType:r,difficulty:a,isDisabled:x,onStartMining:C})]})})]})})]})})};function Oa(t){const r={trash_medical:{location:"Abandoned Clinic",description:"While scavenging, you come across an abandoned clinic in a back alley. Peeking inside, you see overflowing garbage bins and medical supplies scattered around. The clinic's exterior is covered in graffiti, and its interior contains old medical equipment and documents.",choices:{trash:{action:"Scavenge through outdoor trash bins",success:"Digging through the clinic's dumpster, you uncover some usable items beneath the refuse:",failureInjury:"While lifting a heavy bag of medical waste, you feel a sharp pain in your side. You've bruised your ribs.",injury:"fracture_injury",failureJail:"A passing patrol spots you trespassing on private property. You're escorted to the local precinct for a 'chat'."},medical:{action:"Search for medical supplies inside",success:"Amidst the cluttered shelves and drawers, you find some valuable medical supplies:",failureInjury:"You trip over scattered debris, twisting your ankle badly as you fall.",injury:"trauma_injury",failureJail:"Alarms you didn't notice start blaring. Before you can escape, security arrives and detains you."}}},trash_upgrade:{location:"Discarded Electronics Store",description:"While scavenging, you find an old electronics store that has been out of business for years. Piles of outdated gadgets and their packaging litter the floor. Among the trash, you notice components and tools that can be repurposed for upgrades.",choices:{trash:{action:"Sort through piles of outdated gadgets",success:"Sifting through the electronic junk, you salvage some useful items:",failureInjury:"A jagged piece of metal slices your hand as you dig through the pile. The cut is deep and bleeding profusely.",injury:"bleeding_injury",failureJail:"The store owner catches you in the act. He calls the police, and you're taken in for attempted burglary."},upgrade:{action:"Look for repurposable components",success:"You carefully extract some promising components from the old devices:",failureInjury:"After hours of meticulous searching, you're overcome with minor fatigue, your vision blurring from exhaustion.",injury:"fatigue_injury",failureJail:"Your scavenging triggers a silent alarm. The police arrive and arrest you for trespassing and theft."}}},trash_herb:{location:"Neglected Urban Garden",description:"While scavenging, you come across a neglected urban community garden. Trash bags and litter are strewn about, but amidst the neglect, wild herbs and plants still grow.",choices:{trash:{action:"Check litter and trash bags",success:"Rummaging through the garden's debris, you find some discarded but useful items:",failureInjury:"You slip on wet leaves and bang your head against a planter. A mild headache sets in, possibly indicating a minor concussion.",injury:"concussion_injury",failureJail:"A community volunteer spots you and calls the police, reporting you for vandalism."},herb:{action:"Search among overgrown plants",success:"Carefully pushing aside the overgrowth, you discover some thriving herbs:",failureInjury:"You stumble into a thorny bush, leaving your arms covered in cuts and scrapes.",injury:"bleeding_injury",failureJail:"A local gardening club member mistakes you for a plant thief and calls the authorities."}}},trash_tech:{location:"Abandoned Internet Café",description:"While scavenging, you find a once-bustling internet café now left to decay. Broken chairs, food wrappers, and old computers are scattered throughout. Among the trash, you spot some usable tech components.",choices:{trash:{action:"Dig through debris and wrappers",success:"Sorting through layers of café detritus, you unearth some valuable items:",failureInjury:"A precariously balanced pile of old monitors topples onto you, leaving you with painful skin bruises all over.",injury:"contusion_injury",failureJail:"The café owner arrives to check on the property and catches you in the act. Off to jail you go!"},tech:{action:"Inspect old computers and equipment",success:"After a thorough examination of the outdated machines, you extract some useful components:",failureInjury:"You accidentally touch an exposed wire, receiving a shock that leaves you with minor fatigue and muscle weakness.",injury:"fatigue_injury",failureJail:"Your tampering sets off an old security system. The police arrive and arrest you for breaking and entering."}}},trash_ore:{location:"Derelict Construction Site",description:"While scavenging, you arrive at an unfinished construction site, abandoned and filled with debris. Trash piles mix with construction materials, including metal ores and other valuable resources.",choices:{trash:{action:"Rummage through debris and trash",success:"Picking through the construction site's refuse, you recover some usable items:",failureInjury:"A stack of metal pipes shifts unexpectedly, falling on your leg and causing a large skin bruise.",injury:"contusion_injury",failureJail:"A security guard on his rounds spots you trespassing. He calls the police, and you're arrested."},ore:{action:"Search construction materials",success:"Examining the abandoned construction materials, you find some valuable resources:",failureInjury:"While lifting a heavy piece of ore, you strain your back. The trauma to your muscles is immediate and painful.",injury:"trauma_injury",failureJail:"Your activities attract the attention of a passing patrol. They detain you for theft of construction materials."}}},medical_upgrade:{location:"Ransacked Pharmacy",description:"While scavenging, you enter a pharmacy that has been looted. Shelves are overturned, and supplies are scattered. Among the chaos, you find medical supplies mixed with items that can be used to enhance equipment.",choices:{medical:{action:"Look for remaining medical supplies",success:"Searching through the ransacked shelves, you manage to find some untouched medical supplies:",failureInjury:"You slip on spilled liquid medicines, twisting your ankle badly as you fall.",injury:"trauma_injury",failureJail:"The pharmacy's silent alarm is still active. Police arrive and arrest you for looting."},upgrade:{action:"Find items to enhance gear",success:"Among the scattered pharmacy goods, you discover some items perfect for gear enhancement:",failureInjury:"A shelving unit collapses as you search, striking your chest and leaving you with bruised ribs.",injury:"fracture_injury",failureJail:"A vigilant neighbor spots you and reports a break-in. The police arrive swiftly to apprehend you."}}},medical_herb:{location:"Traditional Herbalist Shop",description:"While scavenging, you discover a small herbalist shop in an old district, partially damaged but still containing various herbs and medical supplies. The air is filled with the scent of dried herbs.",choices:{medical:{action:"Search for medical supplies",success:"In the back of the shop, you uncover a cache of well-preserved medical supplies:",failureInjury:"You accidentally inhale powdered herbs, triggering a coughing fit that leaves you with minor fatigue.",injury:"fatigue_injury",failureJail:"The shop owner returns unexpectedly. Mistaking you for a burglar, he calls the police."},herb:{action:"Gather stored herbs",success:"Carefully sorting through the aromatic herbs, you collect some potent specimens:",failureInjury:"You mistake a toxic herb for a harmless one, and handling it causes cuts and scrapes on your hands.",injury:"bleeding_injury",failureJail:"A local herbalist recognizes you're not the shop owner. She alerts the authorities, leading to your arrest."}}},medical_tech:{location:"High-Tech Medical Facility",description:"While scavenging, you come across a state-of-the-art medical research facility that was hastily evacuated. Advanced medical technology and supplies are left behind, ripe for scavenging.",choices:{medical:{action:"Collect advanced medical supplies",success:"From the facility's stores, you gather some cutting-edge medical supplies:",failureInjury:"You bump your head on an open cabinet door, leaving you with a mild headache that could be a minor concussion.",injury:"concussion_injury",failureJail:"The facility's AI security system detects your presence and locks down the building until authorities arrive."},tech:{action:"Scavenge high-tech devices",success:"You manage to salvage some advanced technological devices from the facility:",failureInjury:"An experimental device misfires, zapping you and causing minor fatigue and disorientation.",injury:"fatigue_injury",failureJail:"Your tampering with the equipment triggers a biohazard alarm. A HAZMAT team arrives, followed by the police."}}},medical_ore:{location:"Research Laboratory",description:"While scavenging, you find a laboratory that focuses on medical and material sciences. It contains both medical supplies and samples of various ores used in their experiments.",choices:{medical:{action:"Gather medical research supplies",success:"From the lab's inventory, you collect some specialized medical research supplies:",failureInjury:"You accidentally knock over a heavy medical device, causing trauma to your foot as it lands on it.",injury:"trauma_injury",failureJail:"A late-working scientist catches you in the act and immediately calls security."},ore:{action:"Collect ore samples",success:"You carefully extract some rare ore samples from the laboratory's storage:",failureInjury:"While handling a sample, you cut your hand on a sharp edge. The cut is deep and bleeding.",injury:"bleeding_injury",failureJail:"Your attempts to access a secured ore storage trigger a silent alarm. Security arrives promptly."}}},upgrade_herb:{location:"Rooftop Garden Workshop",description:"While scavenging, you reach a rooftop garden that doubles as a workshop. Amidst the plants and herbs growing in containers, there are tools and materials for crafting and upgrades.",choices:{upgrade:{action:"Search workshop for tools",success:"Rifling through the workshop, you find some useful tools and materials:",failureInjury:"You lose your footing on the wet rooftop and fall, twisting your ankle badly.",injury:"trauma_injury",failureJail:"The building's security spots you on the roof. They detain you until the police arrive."},herb:{action:"Pick herbs from containers",success:"From the rooftop containers, you harvest a selection of healthy herbs:",failureInjury:"The sweltering heat on the rooftop leaves you with minor fatigue and dizziness.",injury:"fatigue_injury",failureJail:"A resident spots you from their window and reports you for trespassing and theft."}}},upgrade_tech:{location:"Innovator's Garage",description:"While scavenging, you enter a tinkerer's garage filled with half-finished projects and cutting-edge technology. Upgrade components are scattered among tech gadgets and tools.",choices:{upgrade:{action:"Find upgrade components",success:"Sifting through the innovator's projects, you salvage some valuable upgrade components:",failureInjury:"An unstable prototype explodes in your hands, leaving you with cuts and scrapes all over your arms.",injury:"bleeding_injury",failureJail:"The garage's owner returns and catches you red-handed. He immediately calls the police."},tech:{action:"Scavenge high-tech gadgets",success:"From the array of gadgets, you manage to extract some advanced technological items:",failureInjury:"A malfunctioning robotic arm swings wildly, striking your chest and leaving you with bruised ribs.",injury:"fracture_injury",failureJail:"Your tinkering activates a high-pitched alarm. Neighbors call the police, who quickly arrive."}}},upgrade_ore:{location:"Mechanic's Yard",description:"While scavenging, you explore an open yard used by a mechanic for salvaging and repurposing old machinery. Metal ores and various upgrade parts are plentiful here.",choices:{upgrade:{action:"Look for upgrade parts",success:"Searching through the mechanic's collection, you find some useful upgrade parts:",failureInjury:"A heavy machine part falls, striking your leg and causing a large skin bruise.",injury:"contusion_injury",failureJail:"The mechanic's guard dogs corner you in the yard. Their barking alerts the police."},ore:{action:"Collect metal ores",success:"From the yard's resources, you gather a variety of valuable metal ores:",failureInjury:"The strain of carrying heavy ores leaves you with minor fatigue and muscle aches.",injury:"fatigue_injury",failureJail:"A night watchman spots you loading ores into your bag. He detains you until the police arrive."}}},herb_tech:{location:"Experimental Bio-Lab",description:"While scavenging, you come across a bio-laboratory that blends nature and technology. Herb samples are grown alongside high-tech devices designed to study and enhance their properties.",choices:{herb:{action:"Gather herb samples",success:"From the bio-lab's greenhouse, you collect some rare and potent herb samples:",failureInjury:"You bump your head on a low-hanging hydroponic system, causing a mild headache that might be a minor concussion.",injury:"concussion_injury",failureJail:"The lab's containment protocols activate, trapping you inside until the authorities arrive."},tech:{action:"Scavenge research devices",success:"You manage to salvage some advanced bio-tech research devices:",failureInjury:"A device designed to analyze plant growth accidentally pricks your skin, leaving you with minor cuts and scrapes.",injury:"bleeding_injury",failureJail:"Your tampering with the lab equipment triggers a biohazard alarm. A response team arrives and detains you."}}},herb_ore:{location:"Botanical Research Center",description:"While scavenging, you find a facility dedicated to studying the natural properties of plants and minerals. Various herbs and mineral ores are stored here for research purposes.",choices:{herb:{action:"Collect research herbs",success:"From the center's vast collection, you gather some unique research-grade herbs:",failureInjury:"You slip on a wet floor, twisting your ankle painfully as you fall.",injury:"trauma_injury",failureJail:"A late-night researcher catches you in the restricted area and calls security."},ore:{action:"Gather mineral ores",success:"Searching through the facility's stores, you collect some rare mineral ore samples:",failureInjury:"While reaching for a high shelf, you strain your back. The muscle trauma is immediate and painful.",injury:"trauma_injury",failureJail:"Your presence triggers a silent alarm. Police arrive to find you surrounded by valuable research materials."}}},tech_ore:{location:"Abandoned Factory",description:"While scavenging, you come across an old factory that manufactured tech devices and components. The place is filled with discarded tech and raw ores used in production, waiting to be scavenged.",choices:{tech:{action:"Search for tech components",success:"Digging through the factory's leftovers, you salvage some valuable tech components:",failureInjury:"You accidentally activate an old industrial laser cutter, which grazes your arm. That's going to leave a scar.",injury:"bleeding_injury",failureJail:"Your rummaging triggers an old security system. Local police respond to the silent alarm and find you amidst the valuable tech."},ore:{action:"Scavenge raw ores",success:"From the factory's raw materials, you gather a selection of useful ores:",failureInjury:"A unstable pile of ore samples collapses as you approach, partially burying you and leaving you with multiple contusions.",injury:"contusion_injury",failureJail:"A security guard on a routine check spots you hauling ore from the premises. He detains you until the authorities arrive."}}}},a=[...t].sort().join("_"),s=a.split("_").reverse().join("_");return r[a]||r[s]||{location:"Unknown",description:"No description available.",choices:{}}}const Ma=()=>{const t=Y();return q(S.explore.makeScavengeChoice.mutationOptions({onSuccess:(r,a,s)=>{t.invalidateQueries({queryKey:S.user.getInventory.key()})}}))},we={container:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},wipe:{hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{ease:"easeInOut",duration:.75}}},dialogueBox:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.3,ease:"easeOut"}}}},za={trash:"https://img.icons8.com/?size=100&id=67367&format=png",medical:"https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png",upgrade:"https://img.icons8.com/?size=100&id=HA930KjFD9ki&format=png",herb:"https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",tech:"https://img.icons8.com/?size=100&id=fzdr7mjSaS_w&format=png",ore:"https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png"},Da=t=>za[t]||yt,La=t=>t?"failureInjury":"failureJail",Ba=()=>{const t=Y();return{invalidateQueries:async()=>{await Promise.all([t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})])}}},Ra=({isSuccess:t})=>{const r=t?"text-green-400":"text-red-400",a=t?"bg-green-500/20":"bg-red-500/20";return e.jsx("div",{className:u("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center",a),children:e.jsx("svg",{className:u("w-8 h-8 md:w-10 md:h-10",r),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})},Pa=({item:t,quantity:r})=>e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 md:gap-4",children:[e.jsxs("span",{className:"text-white text-lg md:text-xl font-semibold",children:[r??1,"x"]}),e.jsx(re,{item:t,height:"h-10 w-auto md:h-12 lg:h-14",className:"inline-block"}),e.jsx("span",{className:u("text-lg md:text-xl font-semibold",Ie(t.rarity)),children:t.name})]})}),Ya=({choice:t,index:r,onSelect:a,isDisabled:s,locationDetails:i})=>{const n=i?.choices?.[t]?.action||"";return e.jsxs("button",{type:"button",disabled:s,className:u("cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-600 to-purple-700","hover:from-indigo-500 hover:to-purple-600 transition-all duration-300","border-2 border-indigo-400/30 hover:border-indigo-400/60","shadow-lg hover:shadow-xl transform hover:scale-[1.02]","disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none","p-6 md:p-8 h-24 md:h-32 lg:h-36"),onClick:()=>a(r+1),children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"relative flex items-center justify-center gap-3 md:gap-4 h-full",children:[e.jsx("img",{src:Da(t),className:"w-8 h-8 md:w-12 md:h-12 lg:w-16 lg:h-16 object-contain",alt:`${t} icon`}),e.jsx("span",{className:"text-white font-semibold text-sm md:text-base lg:text-lg uppercase tracking-wide",children:n})]})]})},Pe=({scavengeResult:t,resultText:r,onAction:a,isLoading:s,isMobile:i,actionButtonText:n,actionButtonVariant:l="primary"})=>e.jsxs("div",{className:"flex flex-col items-center gap-6 md:gap-8 text-center",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(Ra,{isSuccess:t.success}),e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl",children:r})]}),t.success&&t.itemReward&&e.jsx(Pa,{item:t.itemReward,quantity:t.itemQuantity}),!t.success&&t.injury&&e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30",children:e.jsx(Ce,{currentEffects:[{id:1,count:1,debuff:t.injury}],className:"justify-center"})}),e.jsx("div",{className:"w-full max-w-sm",children:e.jsx(ge,{variant:l,size:i?"md":"lg",isLoading:s,className:"w-full text-base md:text-lg",onClick:a,children:n})})]}),Ga=({nodeId:t,choices:r,onClose:a})=>{const s=pe(),[i,n]=p.useState(!1),[l,m]=p.useState(null),c=he(),g=J(),{invalidateQueries:h}=Ba(),{mutate:d,isPending:b}=Ma(),x=Oa(r),w=ae("citystreet1");p.useEffect(()=>{s.start("visible")},[s]);const C=k=>{b||d({nodeId:t,choiceIndex:k},{onSuccess:j=>{m({success:j.success||!1,choice:j.choice||r[k-1],message:j.message||"Scavenging completed",itemReward:j.itemReward,itemQuantity:j.itemQuantity,jailed:j.jailed,jailDuration:j.jailDuration,injury:j.injury})},onError:j=>{m({success:!1,choice:r[k-1],message:j.message||"Something went wrong during scavenging"})}})},v=async()=>{n(!0),await h(),a(),n(!1)},o=async()=>{n(!0),await h(),g("/jail")},A=()=>{if(!l)return null;const k=La(!!l.injury),j=l.success?x?.choices?.[l.choice]?.success||"":x?.choices?.[l.choice]?.[k]||"";return l.success?e.jsx(Pe,{scavengeResult:l,resultText:j,isLoading:i,isMobile:c,actionButtonText:"Return to Explore",onAction:v}):e.jsx(Pe,{scavengeResult:l,resultText:j,isLoading:i,isMobile:c,actionButtonText:l.jailed?"Go to Jail":"Continue Exploring",actionButtonVariant:l.jailed?"destructive":"primary",onAction:l.jailed?o:v})};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",children:e.jsxs(M.div,{className:"relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",variants:we.container,initial:"hidden",animate:s,children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{className:"w-full h-full object-cover",src:w||"",alt:"Scavenging location"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50"})]}),e.jsx(M.div,{className:"absolute inset-0 bg-black z-10",variants:we.wipe,initial:"hidden",animate:"visible"}),e.jsx(M.div,{variants:we.dialogueBox,initial:"hidden",animate:"visible",className:"relative z-20 h-full flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-indigo-500/80 rounded-2xl shadow-2xl",children:[e.jsx("div",{className:"px-6 py-4 border-b border-indigo-500/30",children:e.jsx("h2",{className:"text-center text-indigo-400 text-xl md:text-2xl lg:text-3xl font-semibold",children:x?.location})}),e.jsx("div",{className:"p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col",children:l?e.jsx("div",{className:"flex-1 flex flex-col justify-center",children:A()}):e.jsxs("div",{className:"flex-1 flex flex-col justify-center gap-6 md:gap-8",children:[e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto",children:x?.description})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 max-w-4xl mx-auto w-full",children:b?e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"inline-flex items-center gap-2 text-gray-300 text-sm md:text-base",children:e.jsx("div",{className:"size-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"})})}):r.map((k,j)=>e.jsx(Ya,{choice:k,index:j,isDisabled:b,locationDetails:x,onSelect:C},k))})]})})]})})]})})},U=[{id:"shibuya",name:"Shibuya",cost:100,description:"A bustling commercial and entertainment district known for its shopping and nightlife.",position:{x:45,y:55},background:"bg-gradient-to-br from-pink-900/30 to-purple-900/30",atmosphere:{primaryColor:"from-pink-600 to-purple-700",accentColor:"border-pink-500",glowColor:"shadow-pink-500/20",theme:"neon-commercial"},features:["Shopping Districts","Youth Culture","Busy Crossings"],travelTimes:{walk:15,bus:4}},{id:"shinjuku",name:"Shinjuku",cost:150,description:"A major commercial and administrative center with the world's busiest railway station.",position:{x:35,y:30},background:"bg-gradient-to-br from-red-900/30 to-orange-900/30",atmosphere:{primaryColor:"from-red-600 to-orange-700",accentColor:"border-red-500",glowColor:"shadow-red-500/20",theme:"urban-chaos"},features:["Business District","Government Buildings","Entertainment"],travelTimes:{walk:20,bus:5}},{id:"bunkyo",name:"Bunkyo",cost:200,description:"A district known for its traditional architecture and cultural heritage.",position:{x:65,y:35},background:"bg-gradient-to-br from-blue-900/30 to-cyan-900/30",atmosphere:{primaryColor:"from-blue-600 to-cyan-700",accentColor:"border-blue-500",glowColor:"shadow-blue-500/20",theme:"tech-paradise"},features:["Traditional Architecture","Cultural Heritage","Historical Sites"],travelTimes:{walk:25,bus:7}},{id:"chiyoda",name:"Chiyoda",cost:250,description:"The political center of Tokyo, containing the Imperial Palace and government buildings.",position:{x:55,y:20},background:"bg-gradient-to-br from-emerald-900/30 to-green-900/30",atmosphere:{primaryColor:"from-emerald-600 to-green-700",accentColor:"border-emerald-500",glowColor:"shadow-emerald-500/20",theme:"imperial-formal"},features:["Imperial Palace","Government District","Historic Sites"],travelTimes:{walk:30,bus:8}},{id:"minato",name:"Minato",cost:300,description:"An upscale district with many embassies, corporate headquarters, and Tokyo Tower.",position:{x:70,y:65},background:"bg-gradient-to-br from-indigo-900/30 to-violet-900/30",atmosphere:{primaryColor:"from-indigo-600 to-violet-700",accentColor:"border-indigo-500",glowColor:"shadow-indigo-500/20",theme:"luxury-corporate"},features:["Luxury Shopping","Embassies","Tokyo Tower"],travelTimes:{walk:35,bus:9}}],Qa={tokyo:"https://media.sketchfab.com/models/a05e11c729f84bc4bc3e7d791c461947/thumbnails/445b23029bda4c4297e23c6404909275/9a84ce44cd024d99b00f59a5c8864326.jpeg",shibuya:"https://i.ibb.co/gF71hMMk/shibuya-20000-sdb-7c434f-preview-1.png",shinjuku:"https://i.imgur.com/BLkvr0i.png",bunkyo:"https://i.imgur.com/FqJ1RoJ.png",chiyoda:"https://i.imgur.com/BeEDDBw.png",minato:"https://i.imgur.com/HnOe3Tv.png"},rt=({currentView:t,className:r})=>{const a=t?U.find(s=>s.id===t):null;return e.jsxs("div",{className:`absolute inset-0 w-full h-full ${r||""}`,children:[e.jsx("img",{src:Qa[t],alt:"Map Background",className:u("w-full h-full object-cover",t==="tokyo"&&"scale-150")}),a&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute inset-0 pointer-events-none mix-blend-multiply opacity-30",style:{background:`linear-gradient(to br, ${Jr(a.atmosphere.primaryColor)})`}}),a.atmosphere.theme==="neon-commercial"&&e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsx("div",{className:"absolute w-full h-full opacity-20 animate-pulse",style:{background:`
                                        radial-gradient(ellipse at 20% 30%, rgba(255, 20, 147, 0.3) 0%, transparent 40%),
                                        radial-gradient(ellipse at 80% 70%, rgba(138, 43, 226, 0.3) 0%, transparent 40%)
                                    `}})}),a.atmosphere.theme==="urban-chaos"&&e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsx("div",{className:"absolute w-full h-full opacity-25",style:{background:`
                                        radial-gradient(circle at 30% 20%, rgba(220, 38, 38, 0.3) 0%, transparent 30%),
                                        radial-gradient(circle at 70% 80%, rgba(251, 146, 60, 0.3) 0%, transparent 30%)
                                    `}})}),a.atmosphere.theme==="tech-paradise"&&e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsx("div",{className:"absolute w-full h-full opacity-20",style:{background:`
                                        linear-gradient(45deg, rgba(37, 99, 235, 0.2) 0%, transparent 50%),
                                        linear-gradient(-45deg, rgba(6, 182, 212, 0.2) 0%, transparent 50%)
                                    `}})})]}),e.jsx("div",{className:"absolute inset-0 pointer-events-none opacity-10",style:{backgroundImage:`
                        linear-gradient(to right, rgba(128, 90, 213, 0.2) 1px, transparent 1px),
                        linear-gradient(to bottom, rgba(128, 90, 213, 0.2) 1px, transparent 1px)
                    `,backgroundSize:"50px 50px"}})]})};function Fa({className:t,...r}){return e.jsx("div",{"data-slot":"card",className:u("bg-white text-zinc-950 flex flex-col gap-6 rounded-xl border border-zinc-200 py-6 shadow-sm dark:bg-zinc-950 dark:text-zinc-50 dark:border-zinc-800",t),...r})}function Ha({className:t,...r}){return e.jsx("div",{"data-slot":"card-content",className:u("px-6",t),...r})}const Ua=Qe("inline-flex items-center justify-center rounded-md border border-zinc-200 px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-zinc-950 focus-visible:ring-zinc-950/50 focus-visible:ring-[3px] aria-invalid:ring-red-500/20 dark:aria-invalid:ring-red-500/40 aria-invalid:border-red-500 transition-[color,box-shadow] overflow-hidden dark:border-zinc-800 dark:focus-visible:border-zinc-300 dark:focus-visible:ring-zinc-300/50 dark:aria-invalid:ring-red-900/20 dark:dark:aria-invalid:ring-red-900/40 dark:aria-invalid:border-red-900",{variants:{variant:{default:"border-transparent bg-zinc-900 text-zinc-50 [a&]:hover:bg-zinc-900/90 dark:bg-zinc-50 dark:text-zinc-900 dark:[a&]:hover:bg-zinc-50/90",secondary:"border-transparent bg-zinc-100 text-zinc-900 [a&]:hover:bg-zinc-100/90 dark:bg-zinc-800 dark:text-zinc-50 dark:[a&]:hover:bg-zinc-800/90",destructive:"border-transparent bg-red-500 text-white [a&]:hover:bg-red-500/90 focus-visible:ring-red-500/20 dark:focus-visible:ring-red-500/40 dark:bg-red-500/60 dark:bg-red-900 dark:[a&]:hover:bg-red-900/90 dark:focus-visible:ring-red-900/20 dark:dark:focus-visible:ring-red-900/40 dark:dark:bg-red-900/60",outline:"text-zinc-950 [a&]:hover:bg-zinc-100 [a&]:hover:text-zinc-900 dark:text-zinc-50 dark:[a&]:hover:bg-zinc-800 dark:[a&]:hover:text-zinc-50"}},defaultVariants:{variant:"default"}});function Ye({className:t,variant:r,asChild:a=!1,...s}){const i=a?vt:"span";return e.jsx(i,{"data-slot":"badge",className:u(Ua({variant:r}),t),...s})}const Wa=()=>{const t=Y();return q(S.story.completeEpisode.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.quests.getAvailable.key()}),t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})}}))},Ja=({episodeData:t,onClose:r})=>{const a=J(),s=Wa(),[i,n]=p.useState(0),l=t,m=l?.content,c=m?.scenes||[],g=c[i],h=async()=>{try{if(!l?.id){console.error("No episode ID available");return}await s.mutateAsync({episodeId:l.id,choices:{}}),r()}catch(C){console.error("Failed to complete episode:",C)}},d=()=>{i<c.length-1?n(i+1):h()},b=()=>{i>0&&n(i-1)},x=()=>{n(c.length-1)};if(!l||!m)return e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Episode Not Found"}),e.jsxs(ee,{onClick:()=>a("/explore"),children:[e.jsx(de,{className:"h-4 w-4 mr-2"}),"Back to Explore"]})]})});const w=(i+1)/c.length*100;return e.jsxs("div",{className:"min-h-screen bg-gradient-to-b from-slate-900 to-slate-800 text-white",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-slate-900/90 backdrop-blur-sm border-b border-slate-700",children:e.jsxs("div",{className:"container mx-auto px-4 py-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(ee,{variant:"ghost",size:"sm",className:"text-white hover:bg-slate-700",onClick:()=>a("/explore"),children:[e.jsx(de,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-semibold",children:l.name}),e.jsx(Ye,{variant:"secondary",className:"text-xs",children:l.episodeType})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(ee,{variant:"ghost",size:"sm",className:"text-white hover:bg-slate-700",onClick:x,children:e.jsx(rr,{className:"h-4 w-4"})})})]}),e.jsxs("div",{className:"mt-3",children:[e.jsxs("div",{className:"flex justify-between text-sm text-slate-400 mb-1",children:[e.jsxs("span",{children:["Scene ",i+1," of ",c.length]}),e.jsxs("span",{children:[Math.round(w),"%"]})]}),e.jsx(wt,{value:w,className:"h-2"})]})]})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:g&&e.jsx(Fa,{className:"bg-slate-800/50 border-slate-600 backdrop-blur-sm",children:e.jsx(Ha,{className:"p-8",children:e.jsxs("div",{className:"space-y-6",children:[g.speaker&&e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center",children:e.jsx("span",{className:"text-lg font-bold",children:g.speaker.charAt(0)})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-lg",children:g.speaker}),g.character&&e.jsx("p",{className:"text-sm text-slate-400",children:g.character})]})]}),e.jsx("div",{className:"prose prose-invert max-w-none",children:e.jsx("p",{className:"text-lg leading-relaxed",children:g.text})}),g.effects&&g.effects.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2",children:g.effects.map((C,v)=>e.jsx(Ye,{variant:"outline",className:"text-xs",children:C},v))}),g.type!=="choice"&&e.jsxs("div",{className:"flex justify-between items-center mt-8 pt-6 border-t border-slate-600",children:[e.jsxs(ee,{variant:"ghost",disabled:i===0,className:"text-white hover:bg-slate-700",onClick:b,children:[e.jsx(de,{className:"h-4 w-4 mr-2"}),"Previous"]}),e.jsx(ee,{className:"bg-primary hover:bg-primary/90",disabled:s.isPending,onClick:d,children:i===c.length-1?e.jsx(e.Fragment,{children:s.isPending?e.jsxs(e.Fragment,{children:["Completing...",e.jsx(Bt,{className:"h-4 w-4 ml-2 animate-pulse"})]}):e.jsxs(e.Fragment,{children:["Complete Episode",e.jsx(Ae,{className:"h-4 w-4 ml-2"})]})}):e.jsxs(e.Fragment,{children:["Next",e.jsx(jt,{className:"h-4 w-4 ml-2"})]})})]})]})})})})})]})},qa=({mapData:t,isLoading:r,error:a,className:s,viewType:i,currentView:n})=>{const l=p.useRef(null),[m,c]=p.useState(null),[g,h]=p.useState(!1),{mutate:d,isPending:b}=Qr({onSelectedNodeChange:c}),{setJustJailed:x}=Ue(),w=J(),C=Y(),v=p.useCallback(f=>{c(f)},[]),o=t?.find(f=>f.status==="current"),A=p.useMemo(()=>t?$r(t):[],[t]),k=p.useCallback((f,E)=>{const T=t?.find(Q=>Q.id===f);if(!T||T.status!=="available")return;c(f);const L=T.isStatic;if(T.nodeType==="SHOP"){w(`/shops/${E||T.shopId||f}`);return}d({nodeId:f,isStatic:L})},[t,d,w]),j=async()=>{C.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()}),x(!1),await C.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})},B=async()=>{await C.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})};if(o&&o.nodeType==="CHARACTER_ENCOUNTER"&&o.metadata?.dialogue)return e.jsx(ra,{dialogue:o.metadata.dialogue,healed:o.metadata.healed,nodeId:o.id,onClose:j});if(o&&o.nodeType==="STORY"&&o.metadata?.episodeData)return e.jsx(Ja,{episodeData:o.metadata.episodeData,onClose:B});if(o&&o.nodeType==="SCAVENGE_NODE"&&o.metadata?.choices)return e.jsx(Ga,{nodeId:o.id,location:o.location,choices:o.metadata.choices,onClose:j});if(o&&o.nodeType==="MINING_NODE"&&o.status==="current"){const f=o.metadata?.miningType||"ore",E=o.metadata?.difficulty||"easy";return e.jsx(Ea,{nodeId:o.id,location:o.location,miningType:f,difficulty:E,onClose:j})}if(o&&o.nodeType==="FORAGING_NODE"&&o.status==="current"){const f=o.metadata?.foragingType||"herbs",E=o.metadata?.difficulty||"easy";return e.jsx(Na,{nodeId:o.id,location:o.location,foragingType:f,difficulty:E,onClose:j})}return r?e.jsx(ga,{}):a?e.jsx(xa,{}):i==="map"?e.jsxs("div",{className:`relative ${s||""}`,children:[b&&e.jsx("div",{className:"absolute inset-0 bg-black/20 z-50 flex items-center justify-center rounded-lg",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg flex items-center space-x-2",children:[e.jsx(ue,{className:"w-5 h-5 animate-spin text-blue-500"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"Interacting..."})]})}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4 lg:gap-6",children:[e.jsxs("div",{ref:l,className:"flex-1 min-w-0 relative min-h-[400px] lg:min-h-[600px]",children:[e.jsx("div",{className:"absolute inset-0 overflow-hidden rounded-lg",children:e.jsx(rt,{currentView:n||"shibuya",className:"w-full h-full"})}),!1,e.jsx("div",{className:"relative z-10 w-full h-full",children:e.jsx(Yr,{showTooltips:!0,showAnimations:!0,showGridOverlay:g,nodes:t||[],connections:[],mapType:"isolated",interactive:!1,className:u(Hr({background:"default"}),"bg-transparent border-0 w-full h-full"),onNodeClick:v,onAccessNode:k})})]}),e.jsx("div",{className:"lg:w-80 lg:flex-shrink-0",children:e.jsx(De,{})})]})]}):e.jsxs("div",{className:u("space-y-6 grid grid-cols-1 lg:grid-cols-2 gap-12",s),children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"}),e.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"District Locations"}),e.jsx(Te,{className:"w-4 h-4 text-purple-500 animate-pulse"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Explore what this district has to offer"})]}),e.jsx("div",{className:"space-y-3",children:A.map((f,E)=>{const T=Xr(f.status),L=Kr(f.nodeType),Q=Zr(f.nodeType),R=f.status==="available",V=f.status==="locked",D=R&&!b&&m!==f.id;return e.jsx("div",{className:"relative p-1 -m-1",style:{animationDelay:`${E*50}ms`},children:e.jsxs("button",{disabled:!R||b||m===f.id,className:u(Fr({status:f.status,interactive:D,nodeType:f.nodeType}),Ur({nodeType:f.nodeType}),D&&Q.hoverBorder,"w-full relative min-h-[80px] overflow-hidden",D&&"transform transition-all duration-300 hover:scale-[1.02]"),onClick:()=>R&&k(f.id,f.shopId||null),children:[e.jsx("div",{className:u("absolute inset-0 opacity-5 dark:opacity-10 bg-cover bg-center bg-no-repeat transition-opacity duration-300",D&&"group-hover:opacity-10 dark:group-hover:opacity-20"),style:{backgroundImage:(()=>{const F=_r(f.nodeType);if(!F)return"none";const W=F.match(/bg-\[url\('([^']+)'\)\]/);return W?`url("${W[1]}")`:"none"})()}}),R&&e.jsx("div",{className:u("absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none",D&&"group-hover:opacity-100",Q.shadowColor,"shadow-2xl rounded-xl")}),R&&f.nodeType==="STORY"&&e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-400/20 rounded-xl animate-pulse pointer-events-none"}),e.jsxs("div",{className:"relative z-10 flex items-center gap-3 p-0",children:[e.jsx("div",{className:"relative flex-shrink-0",children:e.jsxs("div",{className:u("w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 text-white shadow-md bg-gradient-to-br relative transition-all duration-300",V&&"grayscale opacity-60",D&&"group-hover:shadow-xl",Vr(f.nodeType)),children:[e.jsx("div",{className:"w-4 h-4",children:qr(f.nodeType)}),V&&e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-4 h-4 rounded-full bg-gray-900 dark:bg-gray-100 border border-gray-700 dark:border-gray-300 flex items-center justify-center",children:e.jsx(me,{className:"w-2 h-2 text-gray-400 dark:text-gray-600"})}),R&&f.nodeType==="STORY"&&e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-amber-500 animate-pulse shadow-md shadow-amber-500/50"}),R&&f.nodeType!=="STORY"&&e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-green-500 animate-pulse shadow-md shadow-green-500/50"})]})}),e.jsxs("div",{className:"flex-1 min-w-0 text-left",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[e.jsx("h3",{className:"text-base font-bold text-gray-900 dark:text-white line-clamp-1",children:f.title}),f.nodeType==="STORY"&&e.jsx(Te,{className:"w-4 h-4 text-amber-500 animate-pulse flex-shrink-0"})]}),e.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0 ml-2",children:[e.jsx("span",{className:u("px-2 py-0.5 rounded text-xs font-bold",L.color,f.nodeType==="STORY"&&"ring-2 ring-amber-300 dark:ring-amber-600"),children:L.text}),!f.isStatic&&f.status!=="available"&&f.status!=="current"&&e.jsx("span",{className:u("px-1.5 py-0.5 rounded text-xs font-medium",T.color),children:T.text})]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 line-clamp-1 leading-tight",children:f.description}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 italic",children:f.nodeType==="STORY"?"Story episode - advances main quest progress":Q.description})]}),f.expiresAt&&!f.isStatic&&e.jsx("div",{className:"mt-2",children:e.jsxs("div",{className:u("flex items-center gap-1 text-xs px-2 py-1 rounded w-fit",te(f.expiresAt)?"bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300":"bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"),children:[e.jsx(G,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:Fe(f.expiresAt)})]})})]})]})]})},f.id)})})]}),e.jsx(De,{})]})},Va=()=>{const t=Y();return q(S.explore.changeMapLocation.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})}}))},at=t=>Je(S.explore.getTravelStatus.queryOptions({...t})),Ka=({isOpen:t,onClose:r,onSelectMethod:a,destination:s,userCash:i,isLoading:n=!1})=>{const l=[{method:"walk",name:"Walk",description:"Free travel, but takes longer",cost:0,time:s.travelTimes.walk,icon:"👟"},{method:"bus",name:"Bus",description:"Faster travel, but costs money",cost:s.cost,time:s.travelTimes.bus,icon:"🚌"}],m=c=>{a(c),r()};return e.jsx(Nt,{open:t,onOpenChange:r,children:e.jsxs(kt,{className:"sm:max-w-md bg-gray-800/95 border-purple-900/30 text-white",children:[e.jsxs(Ct,{className:"space-y-3",children:[e.jsxs(At,{className:"flex items-center gap-3 text-xl font-semibold",children:[e.jsx("div",{className:"size-8 rounded-full bg-blue-500/20 flex items-center justify-center",children:e.jsx(Z,{className:"size-4 text-blue-400"})}),"Travel to ",s.name]}),e.jsxs(It,{className:"text-gray-400",children:["Choose your preferred method of travel to ",s.name,"."]})]}),e.jsx("div",{className:"space-y-3 mt-4",children:l.map(c=>{const g=i>=c.cost,h=!g||n,d=c.method==="walk";return e.jsx("div",{className:u("bg-gray-900/50 rounded-lg p-4 border transition-all duration-200 relative",g?"border-gray-700 hover:border-purple-500 hover:bg-gray-800/70":"border-red-800/50 bg-red-900/10"),children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:u("size-12 rounded-full flex items-center justify-center text-xl flex-shrink-0",d?"bg-green-500/20 border border-green-500/30":"bg-blue-500/20 border border-blue-500/30"),children:c.icon}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h3",{className:"font-semibold text-white text-lg",children:c.name}),!g&&c.cost>0&&e.jsx("span",{className:"text-xs text-red-400 bg-red-900/30 px-2 py-1 rounded-full",children:"Cannot Afford"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:c.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(G,{className:"size-3 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Time"}),e.jsxs("p",{className:"text-sm text-white font-medium",children:[c.time," min"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(qe,{className:"size-3 text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Cost"}),e.jsx("p",{className:"text-sm text-white font-medium",children:c.cost===0?"Free":`¥${c.cost}`})]})]})]}),!g&&c.cost>0&&e.jsxs("div",{className:"mb-3 text-xs text-red-400 bg-red-900/20 p-2 rounded border border-red-800/30",children:["Insufficient funds (need ¥",c.cost-i," more)"]}),e.jsx(ge,{disabled:h,size:"sm",className:u("w-full transition-all duration-200",!g&&"!bg-gray-700 hover:!bg-gray-700 !text-gray-400 !border-gray-600"),onClick:()=>m(c.method),children:n?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),e.jsx("span",{children:"Starting Travel..."})]}):e.jsxs("div",{className:"flex items-center gap-2 -ml-5",children:[d?e.jsx(St,{className:"size-4"}):e.jsx(je,{className:"size-4"}),e.jsx("span",{children:g?`${c.name}`:"Cannot Afford"})]})})]})]})},c.method)})}),e.jsx("div",{className:"mt-4 bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"size-8 rounded-full bg-yellow-500/20 flex items-center justify-center",children:e.jsx(Lt,{className:"size-4 text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Your Cash"}),e.jsxs("p",{className:"text-sm font-medium text-white",children:["¥",i.toLocaleString()]})]})]})})]})})},Xa=({className:t})=>{const{data:r}=ke(),{mutate:a,isPending:s}=Va(),{setExplorePageSetting:i}=Ve(),{data:n,refetch:l}=at({refetchInterval:1e3}),[m,c]=p.useState(null),[g,h]=p.useState(!1),[d,b]=p.useState(null),x=r?.currentMapLocation,w=n?.isTravel||!1,C=o=>{if(!(!U.find(k=>k.id===o)||!r)){if(o===x){X.error("You are already in this location!");return}if(w){X.error("You are already traveling!");return}b(o),h(!0)}},v=o=>{const A=U.find(j=>j.id===d);if(!A||!r||!d)return;const k=o==="walk"?0:A.cost;if(r.cash<k){X.error(`You need ¥${k} to travel by ${o}!`);return}a({location:d,method:o},{onSuccess:()=>{X.success(`Started traveling to ${A.name} by ${o}!`),l(),i("map")},onError:j=>{X.error(j?.message||"Failed to start travel")}})};return r?e.jsxs("div",{className:u("space-y-6",t),children:[e.jsxs("div",{className:"grid lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2 bg-gray-900/70 rounded-lg border border-purple-900/30 overflow-hidden",children:e.jsxs("div",{className:"bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-lg shadow-2xl shadow-gray-900/10 dark:shadow-gray-900/30 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200/50 dark:border-gray-700/50 hidden lg:block",children:[e.jsxs("div",{className:"flex items-center space-x-3 absolute z-10 bg-black/10 top-3 left-2 px-2 rounded-lg",children:[e.jsx(Xe,{className:"w-5 h-5 text-purple-600 dark:text-purple-400"}),e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Tokyo District Map"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Click on any district to view details and travel options"})]}),e.jsxs("div",{className:"relative h-80 lg:h-96",children:[e.jsx(rt,{currentView:"tokyo"}),U.map(o=>e.jsxs("button",{disabled:w,className:u("absolute w-16 h-16 transform -translate-x-1/2 -translate-y-1/2 z-10 transition-all duration-300",m===o.id?"scale-110":"hover:scale-105",w&&"opacity-50 cursor-not-allowed"),style:{left:`${o.position.x}%`,top:`${o.position.y}%`},onClick:()=>!w&&c(o.id),children:[e.jsx("div",{className:`absolute inset-0 rounded-lg blur-md opacity-60 bg-gradient-to-br ${o.atmosphere.primaryColor}`,style:{transform:m===o.id?"scale(1.2)":"scale(1)"}}),e.jsxs("div",{className:u("relative w-16 h-16 rounded-lg flex flex-col items-center justify-center p-1",o.background,`border-2 ${o.atmosphere.accentColor}`,m===o.id&&`${o.atmosphere.glowColor} shadow-lg`),children:[e.jsx(Z,{className:"w-6 h-6 text-white mb-1"}),e.jsx("span",{className:"text-white text-xs font-medium text-center leading-tight",children:o.name})]}),e.jsxs("div",{className:u("absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap px-2 py-1 rounded-md text-xs font-medium",m===o.id?`bg-gradient-to-r ${o.atmosphere.primaryColor} text-white shadow-lg`:"bg-gray-900/80 text-gray-300"),children:[o.name,m===o.id&&e.jsx("div",{className:"absolute inset-0 rounded-md bg-white/20 animate-pulse"})]})]},o.id))]})]})}),e.jsx("div",{className:"space-y-4",children:m?(()=>{const o=U.find(j=>j.id===m);if(!o)return null;const A=o.id===x,k=r.cash>=o.cost;return e.jsxs("div",{className:u("rounded-lg p-3 border-2",o.atmosphere.accentColor,o.background),children:[e.jsx("div",{className:"flex items-center justify-between mb-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:u("w-12 h-12 rounded-lg flex items-center justify-center relative overflow-hidden",o.background,`border-2 ${o.atmosphere.accentColor}`),children:[e.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${o.atmosphere.primaryColor} opacity-60 animate-pulse`}),e.jsx(Z,{className:"w-6 h-6 text-white relative z-10"})]}),e.jsx("div",{children:e.jsx("h3",{className:"text-white font-semibold text-lg",children:o.name})})]})}),e.jsx("p",{className:"text-gray-300 text-sm mb-3 leading-relaxed",children:o.description}),e.jsxs("div",{className:"mb-3",children:[e.jsx("h4",{className:"text-white font-medium text-sm mb-2",children:"Key Features"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:o.features.map((j,B)=>e.jsx("span",{className:"px-2 py-1 bg-gray-800/60 text-gray-300 text-xs rounded-full border border-gray-600",children:j},B))})]}),e.jsxs("div",{className:"flex items-center space-x-1 bg-yellow-50 dark:bg-yellow-900/20 mb-4 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800",children:[e.jsx("p",{className:"text-sm font-semibold text-gray-900 dark:text-white mr-4",children:"Cost:"}),e.jsx(qe,{className:"w-4 h-4 text-yellow-600 dark:text-yellow-400"}),e.jsxs("span",{className:"font-semibold text-yellow-700 dark:text-yellow-300 text-sm",children:["¥",o.cost]})]}),A?e.jsx("div",{className:"flex items-center justify-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800",children:e.jsxs("div",{className:"flex items-center space-x-2 text-purple-700 dark:text-purple-300",children:[e.jsx(Z,{className:"w-4 h-4"}),e.jsx("span",{className:"font-semibold text-sm",children:"You are here!"})]})}):e.jsx("button",{disabled:w||s,className:u("w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 text-sm",!w&&!s?"bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transform hover:scale-105 active:scale-95":"bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"),onClick:()=>C(o.id),children:s?e.jsxs(e.Fragment,{children:[e.jsx(ue,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{children:"Starting Travel..."})]}):w?e.jsxs(e.Fragment,{children:[e.jsx(je,{className:"w-4 h-4"}),e.jsx("span",{children:"Currently Traveling"})]}):e.jsxs(e.Fragment,{children:[e.jsx(je,{className:"w-4 h-4"}),e.jsx("span",{children:"Choose Travel Method"})]})}),!k&&!A&&e.jsxs("div",{className:"mt-2 flex items-center space-x-2 text-red-600 dark:text-red-400 text-xs",children:[e.jsx(Ke,{className:"w-3 h-3"}),e.jsxs("span",{children:["You need ¥",(o.cost-r.cash).toLocaleString()," more"]})]})]})})():e.jsx("div",{className:"bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-lg lg:rounded-2xl shadow-2xl shadow-gray-900/10 dark:shadow-gray-900/30 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden p-3 lg:p-6",children:e.jsxs("div",{className:"lg:text-center flex lg:flex-col items-center justify-center gap-x-2",children:[e.jsx(Qt,{className:"lg:w-12 w-8 h-auto text-gray-400 lg:mx-auto lg:mb-3"}),e.jsx("h3",{className:"lg:text-lg font-semibold text-gray-900 dark:text-white lg:mb-2",children:"Select a Tokyo District"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm hidden lg:block",children:"Click on any district marker on the map to view details and travel options."})]})})})]}),d&&e.jsx(Ka,{isOpen:g,destination:U.find(o=>o.id===d),userCash:r?.cash||0,isLoading:s,onSelectMethod:v,onClose:()=>{h(!1),b(null)}})]}):e.jsx("div",{className:"flex items-center justify-center h-96 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950/50 rounded-lg",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ue,{className:"w-8 h-8 animate-spin text-blue-500"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Loading user data..."})]})})},_a=({travelStatus:t,className:r})=>{const[a,s]=p.useState(t.remainingTime||0),[i,n]=p.useState(0),[l,m]=p.useState(!1),[c,g]=p.useState(0),h=p.useRef(0);p.useEffect(()=>{if(!t.isTravel||!t.travelStartTime||!t.travelEndTime)return;const C=()=>{const o=new Date().getTime(),A=new Date(t.travelStartTime).getTime(),k=new Date(t.travelEndTime).getTime();if(isNaN(A)||isNaN(k)||A>=k){console.error("Invalid travel dates detected");return}const j=k-A,B=o-A,f=Math.max(0,k-o);s(f);const E=Math.min(100,B/j*100);if(Math.abs(E-h.current)>=5&&g(T=>T+1),h.current=E,n(E),f<=0&&(n(100),!l)){const T=U.find(L=>L.id===t.travelingTo);X.success(`Successfully arrived at ${T?.name||"your destination"}!`),m(!0)}};C();const v=setInterval(C,1e3);return()=>clearInterval(v)},[t,l]);const d=C=>{const v=Math.ceil(C/1e3),o=Math.floor(v/60),A=v%60;return`${o}:${A.toString().padStart(2,"0")}`},b=a<=0,x=U.find(C=>C.id===t.travelingTo),w=t.travelMethod==="walk";return e.jsx("div",{className:u("h-full overflow-y-auto p-4 max-w-4xl mx-auto",r),children:e.jsxs(M.div,{className:"space-y-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 border border-purple-900/30",children:[e.jsx(Tt,{mode:"wait",children:b?e.jsxs(M.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"text-center mb-6",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-2",children:[e.jsx("div",{className:"size-10 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(Et,{className:"size-6 text-green-400"})}),e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Journey Complete!"})]}),e.jsxs("p",{className:"text-gray-400",children:["Welcome to ",x?.name||"your destination"]})]},"complete"):e.jsx(M.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"text-center mb-6",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-2 -ml-5",children:[e.jsx("div",{className:"size-10 rounded-full bg-purple-500/20 flex items-center justify-center",children:e.jsx(Zt,{className:"size-6 text-purple-400 animate-pulse"})}),e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Traveling"})]})},"traveling")}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(Ot,{className:"size-4 text-purple-400"}),"Travel Method"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(M.div,{className:u("size-16 rounded-full flex items-center justify-center text-2xl relative",w?"bg-green-500/20 border border-green-500/30":"bg-blue-500/20 border border-blue-500/30"),animate:b?{}:{scale:[1,1.05,1]},transition:{duration:2,repeat:b?0:1/0,ease:"easeInOut"},children:[e.jsx("div",{className:u("size-8"),children:t.travelMethod==="walk"?"👟":"🚌"}),!b&&e.jsx("div",{className:"absolute inset-0 overflow-hidden rounded-full",children:[...Array(3)].map((C,v)=>e.jsx(M.div,{className:u("absolute w-1 h-6 rounded-full opacity-30",w?"bg-green-400":"bg-blue-400"),style:{right:`${8+v*6}px`,top:`${16+v*3}px`},animate:{x:[-15,15],opacity:[0,.6,0]},transition:{duration:1.5,repeat:1/0,delay:v*.2,ease:"easeInOut"}},v))})]}),e.jsx("div",{children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{className:"text-base text-gray-200 capitalize",children:t.travelMethod})})})]})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(Z,{className:"size-4 text-blue-400"}),"Destination"]}),e.jsxs("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-700",children:[e.jsx("h4",{className:"text-lg font-semibold text-white",children:x?.name||"Unknown Destination"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Tokyo Ward"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-purple-900/30",children:[e.jsxs("h3",{className:"text-white font-semibold mb-4 flex items-center gap-2",children:[e.jsx(Rt,{className:"size-4 text-purple-400"}),"Journey Progress"]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400",children:"Progress"}),e.jsxs(M.span,{className:"text-lg font-bold text-white",animate:{scale:[1,1.1,1]},transition:{duration:.3},children:[Math.round(i),"%"]},c)]}),e.jsx("div",{className:"w-full h-3 bg-gray-700 rounded-full overflow-hidden",children:e.jsx(M.div,{initial:{width:0},animate:{width:`${Math.min(i,100)}%`},transition:{duration:.8,ease:"easeOut"},className:u("h-full rounded-full transition-all duration-300",w?"bg-green-500":"bg-purple-500"),children:e.jsx(M.div,{className:"h-full bg-gradient-to-r from-transparent via-white/30 to-transparent",animate:{x:["-100%","100%"]},transition:{duration:2,repeat:b?0:1/0,ease:"easeInOut"}})})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(G,{className:"size-3 text-blue-400"})}),e.jsx("span",{className:"text-sm text-gray-400",children:b?"Arrival Time":"Time Remaining"})]}),e.jsx(M.div,{className:"text-xl font-bold text-white",animate:b?{}:{color:["#ffffff","#a855f7","#ffffff"]},transition:{duration:3,repeat:b?0:1/0,ease:"easeInOut"},children:b?"Arrived!":d(a)}),e.jsx("p",{className:"text-xs text-gray-400",children:b?"Ready to explore!":"Until arrival"})]}),e.jsxs("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(We,{className:"size-3 text-yellow-400"})}),e.jsx("span",{className:"text-sm text-gray-400",children:"Travel Speed"})]}),e.jsx("div",{className:u("text-xl font-bold",w?"text-green-400":"text-blue-400"),children:t.travelMethod==="walk"?"Steady":"Fast"}),e.jsx("p",{className:"text-xs text-gray-400",children:t.travelMethod==="walk"?"Walking pace":"Bus transport"})]})]})]})]})})},Za=(t={})=>Je(S.explore.getMapByLocation.queryOptions({staleTime:6e4,...t}));function cs(){const{data:t}=ke(),{explorePageSetting:r,setExplorePageSetting:a}=Ve(),{data:s}=at(),{data:i,isLoading:n,error:l}=Za(),m=ae("highstreet2"),c=Mt(t?.currentMapLocation||"Unknown"),g=s?.isTravel||!1,h=p.useMemo(()=>{if(!i)return!1;const v=i.find(o=>o.status==="current");return v?["CHARACTER_ENCOUNTER","STORY","SCAVENGE_NODE","MINING_NODE","FORAGING_NODE"].includes(v.nodeType):!1},[i]),d=()=>r==="travel"?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 blur-3xl opacity-60",children:e.jsx("h1",{className:"text-3xl sm:text-5xl lg:text-6xl font-black text-purple-400 tracking-tight",children:"Explore Tokyo"})}),e.jsx("div",{className:"absolute inset-0 blur-xl opacity-40",children:e.jsx("h1",{className:"text-3xl sm:text-5xl lg:text-6xl font-black text-pink-300 tracking-tight",children:"Explore Tokyo"})}),e.jsx("h1",{className:"relative text-3xl sm:text-5xl lg:text-6xl font-black text-white tracking-tight",style:{textShadow:`
                                0 0 20px rgba(168, 85, 247, 0.8),
                                0 0 40px rgba(236, 72, 153, 0.6),
                                0 0 60px rgba(34, 211, 238, 0.4),
                                2px 2px 4px rgba(0, 0, 0, 0.8)
                            `},children:"Explore Tokyo"})]}):c,b=()=>r==="travel"?"Choose your next destination":"Discover what this district has to offer",x=()=>r==="travel"?e.jsxs("div",{className:"flex items-center space-x-2 sm:space-x-3 mt-3",children:[e.jsx(Z,{className:"w-4 h-4 sm:w-5 sm:h-5 text-purple-300 animate-bounce"}),e.jsxs("span",{className:"text-sm sm:text-base text-purple-200 font-semibold",children:[e.jsx("span",{className:"inline",children:"Currently in: "}),e.jsx("span",{className:"text-white font-bold bg-purple-600/20 px-2 sm:px-3 py-1 rounded-full backdrop-blur-sm border border-purple-400/30",children:c})]})]}):null,w=()=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur-lg opacity-60 animate-pulse"}),e.jsx("div",{className:"relative flex rounded-2xl bg-gray-900/80 backdrop-blur-md shadow-2xl border border-white/20 hover:border-white/40 transition-all duration-300",children:r==="travel"?e.jsxs("button",{className:"flex items-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold text-white hover:bg-white/10 transition-all duration-300 rounded-2xl group",onClick:()=>a("map"),children:[e.jsx(de,{className:"w-4 h-4 sm:w-5 sm:h-5 group-hover:-translate-x-1 transition-transform"}),e.jsx("span",{className:"hidden xs:inline sm:inline",children:"Back"})]}):e.jsxs("button",{disabled:g||h,className:u("flex items-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold transition-all duration-300 rounded-2xl group",g||h?"text-gray-400 cursor-not-allowed opacity-60":"text-white hover:bg-white/10"),onClick:()=>!(g||h)&&a("travel"),children:[e.jsx(Kt,{className:u("w-4 h-4 sm:w-5 sm:h-5 transition-transform",!g&&"group-hover:rotate-12")}),e.jsx("span",{className:"inline",children:g?"Traveling...":"Travel"})]})})]}),C=()=>{if(r==="travel")return{primary:"rgba(168, 85, 247, 0.8)",secondary:"rgba(236, 72, 153, 0.6)",tertiary:"rgba(34, 211, 238, 0.4)",textShadow:`
                    0 0 20px rgba(168, 85, 247, 0.8),
                    0 0 40px rgba(236, 72, 153, 0.6),
                    0 0 60px rgba(34, 211, 238, 0.4),
                    2px 2px 4px rgba(0, 0, 0, 0.8)
                `}};return e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 -m-2 md:-m-4",children:[e.jsx(zt,{backgroundImage:m||"",title:d(),subtitle:b(),additionalInfo:x(),actionButton:w(),titleTheme:C(),useNegativeZIndex:!1}),e.jsxs("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 -mt-10",children:[r!=="travel"&&e.jsxs("div",{className:"flex items-center bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl w-fit mb-6 shadow-xl border border-gray-200/50 dark:border-gray-700/50",children:[e.jsxs("button",{disabled:g||h,className:u("px-5 py-2.5 rounded-xl transition-all duration-300 flex items-center gap-3 text-sm font-semibold",(g||h)&&"opacity-50 cursor-not-allowed",r==="map"?"bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25 scale-105":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50"),onClick:()=>!(g||h)&&a("map"),children:[e.jsx(Xe,{className:"w-4 h-4"}),e.jsx("span",{children:"Map View"})]}),e.jsxs("button",{disabled:g||h,className:u("px-5 py-2.5 rounded-xl transition-all duration-300 flex items-center gap-3 text-sm font-semibold",(g||h)&&"opacity-50 cursor-not-allowed",r==="list"?"bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25 scale-105":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50"),onClick:()=>!(g||h)&&a("list"),children:[e.jsx(Wt,{className:"w-4 h-4"}),e.jsx("span",{children:"List View"})]})]}),g&&s?e.jsx("div",{className:"flex items-center justify-center min-h-[400px] lg:min-h-[600px] w-full",children:e.jsx(_a,{travelStatus:s})}):e.jsx(e.Fragment,{children:r==="travel"?e.jsx(Xa,{className:"w-full"}):e.jsx(qa,{mapData:i,isLoading:n,error:l,viewType:r,className:"w-full",currentView:t?.currentMapLocation})})]})]})}export{cs as default};
