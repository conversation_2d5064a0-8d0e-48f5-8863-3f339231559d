import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useCompleteNode = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.explore.completeNode.mutationOptions({
            onSuccess: () => {
                // Invalidate and refetch the explore map to get updated node states
                queryClient.invalidateQueries({ queryKey: api.explore.getMapByLocation.key() });
            },
        })
    );
};

export default useCompleteNode;
