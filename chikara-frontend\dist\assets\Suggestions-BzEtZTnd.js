import{b as V,c as i,e as N,g as I,am as c,r as b,j as e,o as d,D as T,bI as H,t as K,h as f,cP as U,B as z,S as R,M,i as S,ac as J}from"./index-DjvF_jFD.js";const X=(t={})=>V(i.suggestions.getSuggestions.queryOptions({...t})),Y=(t={})=>V(i.suggestions.getVoteHistory.queryOptions({...t})),Z=(t,r={})=>V(i.suggestions.getComments.queryOptions({input:{id:t},...r})),_=()=>{const t=N();return I(i.suggestions.vote.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:i.suggestions.getSuggestions.key()}),t.invalidateQueries({queryKey:i.suggestions.getVoteHistory.key()})},onError:r=>{console.error("Suggestion vote error:",r),c.error(r.message||"Failed to vote on suggestion")}}))},ee=()=>{const t=N();return I(i.suggestions.comment.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:i.suggestions.getComments.key()}),c.success("Comment added successfully!")},onError:r=>{console.error("Suggestion comment error:",r),c.error(r.message||"Failed to add comment")}}))},te=()=>{const t=N();return I(i.suggestions.create.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:i.suggestions.getSuggestions.key()}),c.success("Suggestion created successfully!")},onError:r=>{console.error("Create suggestion error:",r),c.error(r.message||"Failed to create suggestion")}}))};function se({suggestion:t,formatTime:r,currentUser:x}){const[n,o]=b.useState(""),{data:l}=Z(t.id),u=ee(),j=async a=>{if(a.preventDefault(),n===""){c.error("Please enter a comment!");return}if(n.length<5){c.error("Please enter a minimum of 5 characters!");return}try{await u.mutateAsync({id:t.id,message:n}),o("")}catch(g){console.error("Comment error:",g)}},y=a=>{const g=a.target.value,k=g.split(`
`);let m=g;k.length>10?m=k.slice(0,10).join(`
`):g.length>800&&(m=g.substring(0,800)),o(m)};return e.jsxs("div",{className:"mt-2 border-slate-200 border-t pt-4 dark:border-slate-700",children:[e.jsx("h3",{className:"mb-2 rounded-lg bg-black/25 py-1 text-center font-medium text-2xl dark:text-slate-100",children:"Comments"}),e.jsx("div",{className:"mt-4",children:l?.map(a=>e.jsx("div",{className:"flex w-full flex-row border-slate-200 border-t py-4 dark:border-slate-700",children:e.jsxs("div",{className:"w-[97%]",children:[e.jsx("p",{className:"mb-2 w-full whitespace-pre-wrap font-medium text-base text-gray-200",children:a?.message}),e.jsxs("div",{className:"flex items-center",children:[e.jsxs(d,{to:`/profile/${a?.userId}`,className:"mr-2 block font-normal text-blue-400 text-sm",children:[e.jsx(T,{src:a?.user,className:"mr-1 inline h-5 w-auto rounded-full"}),a?.user?.username,e.jsxs("small",{className:"ml-0.5 text-slate-400",children:["#",a?.userId]})]}),e.jsxs("p",{className:"font-medium text-gray-300 text-xs",children:["Posted ",r(a?.createdAt)," ago"]})]})]},a.id)}))}),e.jsx("div",{className:"mt-2 min-w-0 flex-1",children:e.jsxs("form",{onSubmit:j,children:[e.jsx("div",{children:e.jsx("textarea",{id:"comment",name:"comment",rows:3,className:"block w-full rounded-md border border-gray-300 bg-white shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm dark:border-gray-500 dark:bg-gray-700 dark:text-slate-200 dark:text-stroke-sm dark:placeholder:text-gray-300",placeholder:"Add a comment..",value:n,onChange:y})}),e.jsx("div",{className:"flex w-full items-center justify-end",children:e.jsx("button",{className:"mt-3 inline-flex rounded-sm bg-linear-to-b from-blue-500 to-indigo-600 px-3 py-2 text-center font-medium text-sm text-stroke-sm text-white transition duration-150 ease-in-out hover:bg-blue-800 focus:outline-hidden focus:ring-4 focus:ring-blue-300 active:outline-hidden active:ring-4 active:ring-blue-300 dark:active:ring-blue-900 dark:focus:ring-blue-900",children:"Leave Comment"})})]})})]})}const P=t=>!t||t==="New"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":t==="Accepted"?"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300":t==="Completed"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":t==="Denied"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",D=t=>{let r="text-gray-200";return t<0&&(r="text-red-500 text-[1rem]"),t>99&&(r="text-gray-200 text-sm"),t<-99&&(r="text-red-500 text-xs"),r};function oe(){const{isLoading:t,data:r}=X(),{data:x}=Y(),[n,o]=b.useState(null),[l,u]=b.useState("new"),[j,y]=b.useState(!1),[a,g]=H(),k=a.get("type"),m=a.get("id"),O=N(),{data:$}=K(),E=_(),v=r?.map(s=>{const p=x?.find(C=>C.suggestionId===s.id);return{...s,...p?{haveVoted:p.voteType}:{haveVoted:null}}});b.useEffect(()=>{if(k&&u(k),m&&m.length>0){const s=v?.find(p=>p.id===parseInt(m));o(s)}},[m,r,x]);const Q=s=>l===s,A=v?.filter(s=>!s.state||s.state==="New"),q=v?.filter(s=>s.state==="Accepted"),B=v?.filter(s=>s.state==="Completed"),F=v?.filter(s=>s.state==="Denied"),G=[{title:"New",type:"new",amount:A?.length},{title:"Accepted",type:"accepted",amount:q?.length},{title:"Completed",type:"completed",amount:B?.length},{title:"Denied",type:"denied",amount:F?.length}];let h=[];(!l||l==="new")&&(h=A),l==="accepted"&&(h=q),l==="completed"&&(h=B),l==="denied"&&(h=F);const W=s=>{u(s),o(null)};async function L(s,p,C=!0){try{const w=await E.mutateAsync({suggestionId:s.id,vote:p});return C&&o({...s,upvotes:w.upvotes,downvotes:w.downvotes,haveVoted:p}),w}catch(w){console.error("Vote error:",w)}}return e.jsx("div",{className:"mt-6 mb-8 md:mx-auto md:my-0 md:max-w-6xl",children:e.jsxs("div",{className:"mx-1 mb-6 rounded-lg bg-white px-6 py-4 pb-2 shadow-sm md:mx-0 dark:border dark:border-slate-700 dark:bg-slate-800",children:[e.jsxs("div",{className:"mb-2 flex flex-col items-center justify-between border-gray-200 border-b pb-2 md:flex-row dark:border-gray-700",children:[e.jsx("div",{children:e.jsx("ul",{className:"flex flex-wrap items-center gap-3 py-2 font-medium text-gray-700 text-sm md:gap-5",children:G?.map(s=>e.jsx("li",{className:"block",children:e.jsxs(d,{to:`/suggestions?type=${s.type}`,className:f(Q(s.type)?"bg-linear-to-b from-blue-500 to-indigo-600 text-white hover:bg-blue-800":"text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-slate-900 dark:hover:text-gray-100 ","inline-block rounded-sm px-3 py-2 text-center text-base transition duration-150 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-indigo-300 active:outline-hidden active:ring-2 active:ring-indigo-300 dark:active:ring-indigo-900 dark:focus:ring-indigo-900"),onClick:()=>W(s.type),children:[" ",s.title," ",s.amount&&s.amount>0?e.jsx("span",{className:"ml-1 hidden h-4 min-w-4 items-center justify-center rounded-sm border border-black bg-yellow-500 px-1 text-white text-xs md:inline-flex",children:s.amount}):e.jsx("span",{className:"ml-1 hidden h-4 min-w-4 items-center justify-center rounded-sm border border-black bg-gray-500 px-1 text-white text-xs md:inline-flex",children:"0"})]})},s.title))})}),e.jsx("div",{children:e.jsx("ul",{className:"flex flex-wrap items-center py-2 font-medium text-gray-700 text-sm",children:e.jsxs("div",{className:"mr-16 flex gap-5 md:mr-0 md:gap-3",children:[e.jsx(d,{to:"/polls",children:e.jsx(U,{className:"my-auto size-9 fill-white/65",iconClassName:"h-[1.3rem] w-[1.3rem] fill-white/65",icon:"poll"})}),e.jsxs(z,{type:"primary",className:"text-stroke-0! text-sm!",onClick:()=>y(!0),children:["✨ ",e.jsx("span",{className:"mx-1 dark:text-stroke-sm",children:"Make A Suggestion"})," ✨"]})]})})})]}),e.jsx("div",{children:e.jsx(e.Fragment,{children:t?e.jsx(R,{center:!0}):e.jsx(e.Fragment,{children:!h||h.length===0?e.jsx("p",{className:"my-5 text-center text-gray-100 text-xl",children:"No suggestions here right now"}):e.jsx(e.Fragment,{children:n?e.jsx(ae,{suggestion:n,suggestionVote:L,queryClient:O,currentUser:$}):h?.map(s=>e.jsx(re,{suggestion:s,setCurrentSuggestion:o},s.id))})})})}),e.jsx(ne,{modalOpen:j,setModalOpen:y,suggestionVote:L})]})})}const re=({suggestion:t})=>{const r=t.upvotes-t.downvotes;return e.jsxs("div",{className:"flex items-center py-4",children:[e.jsx(d,{to:`/suggestions?id=${t.id}`,children:e.jsxs("button",{className:"flex h-[63px] max-w-[35px] flex-col items-center rounded-sm bg-gray-100 p-2 hover:bg-gray-200 dark:bg-slate-900 dark:text-gray-200",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"19",height:"19",className:"main-grid-item-icon",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",children:[e.jsx("line",{x1:"12",x2:"12",y1:"19",y2:"5"}),e.jsx("polyline",{points:"5 12 12 5 19 12"})]}),e.jsx("p",{className:f("text-lg",D(r)),children:r})]})}),e.jsxs("div",{className:"mx-4 flex-1",children:[e.jsx(d,{to:`/suggestions?id=${t.id}`,children:e.jsx("div",{className:"mb-1 inline-block cursor-pointer text-[0.95rem] text-slate-800 md:text-base dark:text-gray-100",children:t.title})}),e.jsxs("div",{className:"mb-1 flex items-center md:mb-0",children:[e.jsx("p",{className:f("mr-2 rounded-full px-2.5 py-0.5 font-medium text-xs",P(t.state)),children:t.state?M(t.state):"New"}),e.jsxs("p",{className:"mr-4 font-normal text-gray-300 text-sm",children:["Created ",S(t?.createdAt)," ago"]}),e.jsxs(d,{to:`/profile/${t.userId}`,className:"hidden font-normal text-blue-500 text-sm md:block dark:text-blue-400",children:[e.jsx(T,{src:t.user,className:"mr-1 inline h-5 w-auto rounded-full"}),t?.user?.username,e.jsxs("small",{className:"ml-0.5 text-slate-400",children:["#",t.userId]})]})]}),e.jsxs(d,{to:`/profile/${t?.userId}`,className:"font-normal text-blue-500 text-sm md:hidden dark:text-blue-400",children:[t?.user?.username,e.jsxs("small",{className:"ml-0.5 text-slate-400",children:["#",t.userId]})]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsxs(d,{to:`/suggestions?id=${t.id}`,children:[e.jsxs("div",{className:"block cursor-pointer font-normal text-slate-800 text-sm dark:text-slate-100",children:[t?.totalComments," comment",t?.totalComments!==1?"s":""]}),e.jsx("div",{className:"block cursor-pointer font-normal text-slate-500 text-xs dark:text-slate-400",children:"View Details →"})]})})]},t.id)},ae=({suggestion:t,suggestionVote:r,currentUser:x})=>{const n=t.upvotes-t.downvotes;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative flex items-center py-4",children:[e.jsxs(d,{to:`/profile/${t?.userId}`,className:"-top-0.5 absolute right-2 flex md:top-2 md:right-5",children:[e.jsx(T,{src:t.user,className:"mr-2 inline h-5 w-auto rounded-full"}),e.jsxs("p",{className:"text-blue-500",children:[t?.user?.username," ",e.jsxs("small",{className:"text-gray-400",children:["#",t.userId]})]})," "]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("button",{className:f(t.haveVoted==="upvote"?"bg-blue-500":"bg-gray-100 dark:bg-slate-900 dark:hover:bg-slate-600","flex flex-col items-center rounded-sm p-2 dark:text-gray-200"),onClick:()=>r(t,"upvote"),children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"19",height:"19",className:"main-grid-item-icon",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",children:[e.jsx("line",{x1:"12",x2:"12",y1:"19",y2:"5"}),e.jsx("polyline",{points:"5 12 12 5 19 12"})]})}),e.jsx("div",{className:"flex flex-col items-center rounded-sm bg-gray-100 p-1 dark:bg-blue-600 dark:text-gray-200",children:e.jsx("p",{className:f("text-lg",D(n)),children:n})}),e.jsx("button",{className:f(t.haveVoted==="downvote"?"bg-blue-500":"bg-gray-100 dark:bg-slate-900 dark:hover:bg-slate-600","flex flex-col items-center rounded-sm p-2 dark:text-gray-200"),onClick:()=>r(t,"downvote"),children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"19",height:"19",className:"main-grid-item-icon rotate-180",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",children:[e.jsx("line",{x1:"12",x2:"12",y1:"19",y2:"5"}),e.jsx("polyline",{points:"5 12 12 5 19 12"})]})})]}),e.jsxs("div",{className:"mx-4 flex-1",children:[e.jsx("h2",{className:"mb-2 font-medium text-slate-800 text-xl md:text-2xl dark:text-slate-100",children:t.title}),e.jsx("p",{className:"mb-2 w-full whitespace-pre-wrap font-medium text-base text-slate-700 dark:text-slate-200",children:t.content}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:f(P(t.state),"mr-2 rounded-full px-2.5 py-0.5 font-medium text-xs"),children:t.state?M(t.state):"New"}),e.jsxs("p",{className:"font-medium text-gray-300 text-xs",children:["Created ",S(t?.createdAt)," ago"]})]})]})]}),e.jsx(se,{formatTime:S,suggestion:t,currentUser:x})]})},ne=({modalOpen:t,setModalOpen:r,suggestionVote:x})=>{const[n,o]=b.useState(""),[l,u]=b.useState(""),j=te();async function y(){if(n.length<5){c.error("Please enter a title");return}if(l.length<10){c.error("Please enter suggestion details");return}try{const a=await j.mutateAsync({title:n,content:l});return x(a,"upvote",!1),o(""),u(""),r(!1),a}catch(a){console.error("Create suggestion error:",a)}}return e.jsx(J,{showClose:!0,open:t,title:"Make A Suggestion",iconBackground:"shadow-lg",modalMaxWidth:"max-w-3xl!",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/cgBzeee.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:r,children:e.jsxs("div",{className:"flex flex-col md:mx-12",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"inputtitle",children:["Title",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"inputtitle",id:"inputtitle",className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",value:n,onChange:a=>{o(a.target.value)}})})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"about",className:"mt-4 mb-2 block font-bold text-gray-700 text-sm uppercase tracking-wide dark:font-normal dark:text-gray-300",children:["Details",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 mb-4",children:e.jsx("textarea",{id:"about",name:"about",rows:3,className:"mt-1 block h-36 w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm md:h-52 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",value:l,onChange:a=>{u(a.target.value)}})})]}),e.jsx("button",{type:"button",className:"darkBlueButtonBGSVG mx-auto flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 dark:text-slate-200",onClick:()=>y(),children:"Submit"})]})})};export{oe as default};
