import{r as t,m as a,b_ as et,b$ as tt,c0 as st,c1 as Se,c2 as rt,c3 as nt,c4 as ot,c5 as xe,c6 as at,c7 as ct,c8 as Ue,c9 as lt,ca as ce,bz as Ve,cb as it,cc as ut,cd as dt,ce as Ee,cf as pt,cg as mt,ch as ht,ci as ft,cj as Ct,ck as gt,cl as ke,cm as yt,cn as vt,co as Rt,cp as St,cq as wt,cr as bt,cs as Et,ct as fe,cu as Pt,cv as kt,cw as Mt,cx as Ft,cy as xt}from"./index-DjvF_jFD.js";var ee=a.createContext({}),_e=a.createContext("default"),Re=(e,s,r,o)=>{if(!e||e.componentFromFramework||s.isDestroyed())return;const u=e.newAgStackInstance();let i,c,l=!1;return u.then(d=>{if(l){s.destroyBean(d);return}i=d,c=i.getGui(),r.appendChild(c),Ie(o,i)}),()=>{l=!0,i&&(c?.parentElement?.removeChild(c),s.destroyBean(i),o&&Ie(o,void 0))}},Ie=(e,s)=>{if(e)if(e instanceof Function)e(s);else{const r=e;r.current=s}},oe=(...e)=>e.filter(r=>r!=null&&r!=="").join(" "),ae=class Le{constructor(...s){this.classesMap={},s.forEach(r=>{this.classesMap[r]=!0})}setClass(s,r){if(!!this.classesMap[s]==r)return this;const n=new Le;return n.classesMap={...this.classesMap},n.classesMap[s]=r,n}toString(){return Object.keys(this.classesMap).filter(r=>this.classesMap[r]).join(" ")}},Ce=e=>{const s=()=>typeof Symbol=="function"&&Symbol.for,r=()=>s()?Symbol.for("react.memo"):60115;return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===r()},Fe=a.version?.split(".")[0],je=Fe==="16"||Fe==="17";function Gt(){return Fe==="19"}var we=!1;function At(e){return we||setTimeout(()=>we=!1,0),we=!0,e()}var ve=(e,s)=>{!je&&e&&!we?at.flushSync(s):s()},Bt=e=>{je?e():a.startTransition(e)};function Dt(e,s,r){return a.useSyncExternalStore?a.useSyncExternalStore(e,s):r}function be(e,s,r){if(s==null||e==null)return s;if(e===s||s.length===0&&e.length===0)return e;if(r||e.length===0&&s.length>0||e.length>0&&s.length===0)return s;const o=[],n=[],u=new Map,i=new Map;for(let c=0;c<s.length;c++){const l=s[c];i.set(l.instanceId,l)}for(let c=0;c<e.length;c++){const l=e[c];u.set(l.instanceId,l),i.has(l.instanceId)&&o.push(l)}for(let c=0;c<s.length;c++){const l=s[c],d=l.instanceId;u.has(d)||n.push(l)}return o.length===e.length&&n.length===0?e:o.length===0&&n.length===s.length?s:o.length===0?n:n.length===0?o:[...o,...n]}var ze=t.forwardRef((e,s)=>{const{registry:r,context:o}=t.useContext(ee),n=t.useRef(null),u=t.useRef(null),i=t.useRef(null),c=t.useRef(null),l=t.useRef(null),d=t.useRef(),[p,E]=t.useState(),[v,h]=t.useState(),[w,R]=t.useState(),[y,m]=t.useState(()=>new ae),[f,A]=t.useState(()=>new ae("ag-hidden")),[G,g]=t.useState(()=>new ae("ag-hidden")),[W,D]=t.useState(()=>new ae("ag-invisible"));t.useImperativeHandle(s,()=>({refresh(){return!1}})),t.useLayoutEffect(()=>Re(p,o,u.current),[p]);const V=t.useCallback(U=>{if(n.current=U,!U){d.current=o.destroyBean(d.current);return}const k={setInnerRenderer:(O,x)=>{E(O),R(x)},setChildCount:O=>h(O),toggleCss:(O,x)=>m(z=>z.setClass(O,x)),setContractedDisplayed:O=>g(x=>x.setClass("ag-hidden",!O)),setExpandedDisplayed:O=>A(x=>x.setClass("ag-hidden",!O)),setCheckboxVisible:O=>D(x=>x.setClass("ag-invisible",!O)),setCheckboxSpacing:O=>D(x=>x.setClass("ag-group-checkbox-spacing",O))},B=r.createDynamicBean("groupCellRendererCtrl",!0);B&&(d.current=o.createBean(B),d.current.init(k,U,i.current,c.current,l.current,ze,e))},[]),N=t.useMemo(()=>`ag-cell-wrapper ${y.toString()}`,[y]),F=t.useMemo(()=>`ag-group-expanded ${f.toString()}`,[f]),b=t.useMemo(()=>`ag-group-contracted ${G.toString()}`,[G]),C=t.useMemo(()=>`ag-group-checkbox ${W.toString()}`,[W]),S=p?.componentFromFramework,L=S?p.componentClass:void 0,P=p==null&&w!=null,H=lt(w);return a.createElement("span",{className:N,ref:V,...e.colDef?{}:{role:d.current?.getCellAriaRole()}},a.createElement("span",{className:F,ref:c}),a.createElement("span",{className:b,ref:l}),a.createElement("span",{className:C,ref:i}),a.createElement("span",{className:"ag-group-value",ref:u},P?H:S?a.createElement(L,{...p.params}):null),a.createElement("span",{className:"ag-group-child-count"},v))}),Oe=ze,Ge=t.createContext({setMethods:()=>{}}),It=e=>{const{initialProps:s,addUpdateCallback:r,CustomComponentClass:o,setMethods:n}=e,[{key:u,...i},c]=t.useState(s);return t.useEffect(()=>{r(l=>c(l))},[]),a.createElement(Ge.Provider,{value:{setMethods:n}},a.createElement(o,{key:u,...i}))},Ot=t.memo(It),Wt=0;function We(){return`agPortalKey_${++Wt}`}var $e=class{constructor(e,s,r,o){this.portal=null,this.oldPortal=null,this.reactComponent=e,this.portalManager=s,this.componentType=r,this.suppressFallbackMethods=!!o,this.statelessComponent=this.isStateless(this.reactComponent),this.key=We(),this.portalKey=We(),this.instanceCreated=this.isStatelessComponent()?ce.resolve(!1):new ce(n=>{this.resolveInstanceCreated=n})}getGui(){return this.eParentElement}getRootElement(){return this.eParentElement.firstChild}destroy(){this.componentInstance&&typeof this.componentInstance.destroy=="function"&&this.componentInstance.destroy();const e=this.portal;e&&this.portalManager.destroyPortal(e)}createParentElement(e){const s=this.portalManager.getComponentWrappingElement(),r=document.createElement(s||"div");return r.classList.add("ag-react-container"),e.reactContainer=r,r}statelessComponentRendered(){return this.eParentElement.childElementCount>0||this.eParentElement.childNodes.length>0}getFrameworkComponentInstance(){return this.componentInstance}isStatelessComponent(){return this.statelessComponent}getReactComponentName(){return this.reactComponent.name}getMemoType(){return this.hasSymbol()?Symbol.for("react.memo"):60115}hasSymbol(){return typeof Symbol=="function"&&Symbol.for}isStateless(e){return typeof e=="function"&&!(e.prototype&&e.prototype.isReactComponent)||typeof e=="object"&&e.$$typeof===this.getMemoType()}hasMethod(e){const s=this.getFrameworkComponentInstance();return!!s&&s[e]!=null||this.fallbackMethodAvailable(e)}callMethod(e,s){const r=this.getFrameworkComponentInstance();if(this.isStatelessComponent())return this.fallbackMethod(e,s&&s[0]?s[0]:{});if(!r){setTimeout(()=>this.callMethod(e,s));return}const o=r[e];if(o)return o.apply(r,s);if(this.fallbackMethodAvailable(e))return this.fallbackMethod(e,s&&s[0]?s[0]:{})}addMethod(e,s){this[e]=s}init(e){return this.eParentElement=this.createParentElement(e),this.createOrUpdatePortal(e),new ce(s=>this.createReactComponent(s))}createOrUpdatePortal(e){this.isStatelessComponent()||(this.ref=s=>{this.componentInstance=s,this.resolveInstanceCreated?.(!0),this.resolveInstanceCreated=void 0},e.ref=this.ref),this.reactElement=this.createElement(this.reactComponent,{...e,key:this.key}),this.portal=Ve.createPortal(this.reactElement,this.eParentElement,this.portalKey)}createElement(e,s){return t.createElement(e,s)}createReactComponent(e){this.portalManager.mountReactPortal(this.portal,this,e)}rendered(){return this.isStatelessComponent()&&this.statelessComponentRendered()||!!(!this.isStatelessComponent()&&this.getFrameworkComponentInstance())}refreshComponent(e){this.oldPortal=this.portal,this.createOrUpdatePortal(e),this.portalManager.updateReactPortal(this.oldPortal,this.portal)}fallbackMethod(e,s){const r=this[`${e}Component`];if(!this.suppressFallbackMethods&&r)return r.bind(this)(s)}fallbackMethodAvailable(e){return this.suppressFallbackMethods?!1:!!this[`${e}Component`]}};function Pe(e,s,r){e.forEach(o=>{const n=s[o];n&&(r[o]=n)})}var re=class extends $e{constructor(){super(...arguments),this.awaitUpdateCallback=new ce(e=>{this.resolveUpdateCallback=e}),this.wrapperComponent=Ot}init(e){return this.sourceParams=e,super.init(this.getProps())}addMethod(){}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}getFrameworkComponentInstance(){return this}createElement(e,s){return super.createElement(this.wrapperComponent,{initialProps:s,CustomComponentClass:e,setMethods:r=>this.setMethods(r),addUpdateCallback:r=>{this.updateCallback=()=>(r(this.getProps()),new ce(o=>{setTimeout(()=>{o()})})),this.resolveUpdateCallback()}})}setMethods(e){this.providedMethods=e,Pe(this.getOptionalMethods(),this.providedMethods,this)}getOptionalMethods(){return[]}getProps(){return{...this.sourceParams,key:this.key,ref:this.ref}}refreshProps(){return this.updateCallback?this.updateCallback():new ce(e=>this.awaitUpdateCallback.then(()=>{this.updateCallback().then(()=>e())}))}},Tt=class extends re{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},Nt=class extends re{constructor(){super(...arguments),this.date=null,this.onDateChange=e=>this.updateDate(e)}getDate(){return this.date}setDate(e){this.date=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached","setInputPlaceholder","setInputAriaLabel","setDisabled"]}updateDate(e){this.setDate(e),this.sourceParams.onDateChanged()}getProps(){const e=super.getProps();return e.date=this.date,e.onDateChange=this.onDateChange,delete e.onDateChanged,e}},Ht=class extends re{constructor(){super(...arguments),this.label="",this.icon=null,this.shake=!1}setIcon(e,s){this.icon=e,this.shake=s,this.refreshProps()}setLabel(e){this.label=e,this.refreshProps()}getProps(){const e=super.getProps(),{label:s,icon:r,shake:o}=this;return e.label=s,e.icon=r,e.shake=o,e}},Ut=class extends re{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e),this.onUiChange=()=>this.sourceParams.filterModifiedCallback(),this.expectingNewMethods=!0,this.hasBeenActive=!1,this.awaitSetMethodsCallback=new ce(e=>{this.resolveSetMethodsCallback=e})}isFilterActive(){return this.model!=null}doesFilterPass(e){return this.providedMethods.doesFilterPass(e)}getModel(){return this.model}setModel(e){return this.expectingNewMethods=!0,this.model=e,this.hasBeenActive||(this.hasBeenActive=this.isFilterActive()),this.refreshProps()}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}afterGuiAttached(e){const s=this.providedMethods;s?s.afterGuiAttached?.(e):this.awaitSetMethodsCallback.then(()=>this.providedMethods?.afterGuiAttached?.(e))}getOptionalMethods(){return["afterGuiDetached","onNewRowsLoaded","getModelAsString","onAnyFilterChanged"]}setMethods(e){this.expectingNewMethods===!1&&this.hasBeenActive&&this.providedMethods?.doesFilterPass!==e?.doesFilterPass&&setTimeout(()=>{this.sourceParams.filterChangedCallback()}),this.expectingNewMethods=!1,super.setMethods(e),this.resolveSetMethodsCallback(),this.resolveFilterPassCallback?.(),this.resolveFilterPassCallback=void 0}updateModel(e){this.resolveFilterPassCallback?.();const s=new ce(r=>{this.resolveFilterPassCallback=r});this.setModel(e).then(()=>{s.then(()=>{this.sourceParams.filterChangedCallback()})})}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e.onUiChange=this.onUiChange,delete e.filterChangedCallback,e}},Vt=class extends re{constructor(){super(...arguments),this.awaitSetMethodsCallback=new ce(e=>{this.resolveSetMethodsCallback=e})}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}afterGuiAttached(e){const s=this.providedMethods;s?s.afterGuiAttached?.(e):this.awaitSetMethodsCallback.then(()=>this.providedMethods?.afterGuiAttached?.(e))}getOptionalMethods(){return["afterGuiDetached","onNewRowsLoaded","onAnyFilterChanged"]}setMethods(e){super.setMethods(e),this.resolveSetMethodsCallback()}};function Je(e,s){e.parentFilterInstance(r=>{(r.setModel(s)||ce.resolve()).then(()=>{e.filterParams.filterChangedCallback()})})}var _t=class{constructor(e,s){this.floatingFilterParams=e,this.refreshProps=s,this.model=null,this.onModelChange=r=>this.updateModel(r)}getProps(){return{...this.floatingFilterParams,model:this.model,onModelChange:this.onModelChange}}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.floatingFilterParams=e,this.refreshProps()}setMethods(e){Pe(this.getOptionalMethods(),e,this)}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),Je(this.floatingFilterParams,e)}},Lt=class extends re{constructor(){super(...arguments),this.model=null,this.onModelChange=e=>this.updateModel(e)}onParentModelChanged(e){this.model=e,this.refreshProps()}refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached"]}updateModel(e){this.model=e,this.refreshProps(),Je(this.sourceParams,e)}getProps(){const e=super.getProps();return e.model=this.model,e.onModelChange=this.onModelChange,e}},jt=class extends re{refresh(e){this.sourceParams=e,this.refreshProps()}getOptionalMethods(){return["afterGuiAttached"]}},zt=class extends re{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},$t=class extends re{refresh(e){this.sourceParams=e,this.refreshProps()}},Jt=class extends re{constructor(){super(...arguments),this.active=!1,this.expanded=!1,this.onActiveChange=e=>this.updateActive(e)}setActive(e){this.awaitSetActive(e)}setExpanded(e){this.expanded=e,this.refreshProps()}getOptionalMethods(){return["select","configureDefaults"]}awaitSetActive(e){return this.active=e,this.refreshProps()}updateActive(e){const s=this.awaitSetActive(e);e&&s.then(()=>this.sourceParams.onItemActivated())}getProps(){const e=super.getProps();return e.active=this.active,e.expanded=this.expanded,e.onActiveChange=this.onActiveChange,delete e.onItemActivated,e}},qt=class extends re{refresh(e){this.sourceParams=e,this.refreshProps()}},Kt=class extends re{refresh(e){return this.sourceParams=e,this.refreshProps(),!0}},Qt=class extends re{constructor(){super(...arguments),this.onStateChange=e=>this.updateState(e)}refresh(e){return this.sourceParams=e,this.refreshProps(),!0}getState(){return this.state}updateState(e){this.state=e,this.refreshProps(),this.sourceParams.onStateUpdated()}getProps(){const e=super.getProps();return e.state=this.state,e.onStateChange=this.onStateChange,e}};function Ae(){xe(231)}var Yt=1e3,Zt=class{constructor(e,s,r){this.destroyed=!1,this.portals=[],this.hasPendingPortalUpdate=!1,this.wrappingElement=s||"div",this.refresher=e,this.maxComponentCreationTimeMs=r||Yt}getPortals(){return this.portals}destroy(){this.destroyed=!0}destroyPortal(e){this.portals=this.portals.filter(s=>s!==e),this.batchUpdate()}getComponentWrappingElement(){return this.wrappingElement}mountReactPortal(e,s,r){this.portals=[...this.portals,e],this.waitForInstance(s,r),this.batchUpdate()}updateReactPortal(e,s){this.portals[this.portals.indexOf(e)]=s,this.batchUpdate()}batchUpdate(){this.hasPendingPortalUpdate||(setTimeout(()=>{this.destroyed||(this.refresher(),this.hasPendingPortalUpdate=!1)}),this.hasPendingPortalUpdate=!0)}waitForInstance(e,s,r=Date.now()){if(this.destroyed){s(null);return}if(e.rendered())s(e);else{if(Date.now()-r>=this.maxComponentCreationTimeMs&&!this.hasPendingPortalUpdate){ve(!0,()=>this.refresher()),e.rendered()&&s(e);return}window.setTimeout(()=>{this.waitForInstance(e,s,r)})}}},Xt=({ctrl:e})=>{const s=e.isAlive(),{context:r}=t.useContext(ee),o=s?e.column.getColId():void 0,[n,u]=t.useState(),[i,c]=t.useState(),l=t.useRef(),d=t.useRef(null),p=t.useRef(null),E=t.useRef(null),v=t.useRef(),h=t.useRef();s&&!h.current&&(h.current=new Ee(()=>d.current));const w=t.useCallback(f=>{if(d.current=f,l.current=f?r.createBean(new fe):r.destroyBean(l.current),!f||!e.isAlive())return;const A=()=>{const g=e.getSelectAllGui();g&&(p.current?.insertAdjacentElement("afterend",g),l.current.addDestroyFunc(()=>g.remove()))},G={setWidth:g=>{d.current&&(d.current.style.width=g)},toggleCss:(g,W)=>h.current.toggleCss(g,W),setUserStyles:g=>c(g),setAriaSort:g=>{d.current&&(g?Pt(d.current,g):kt(d.current))},setUserCompDetails:g=>u(g),getUserCompInstance:()=>v.current||void 0,refreshSelectAllGui:A,removeSelectAllGui:()=>e.getSelectAllGui()?.remove()};e.setComp(G,f,p.current,E.current,l.current),A()},[]);t.useLayoutEffect(()=>Re(n,r,E.current,v),[n]),t.useEffect(()=>{e.setDragSource(d.current)},[n]);const R=t.useMemo(()=>!!(n?.componentFromFramework&&Ce(n.componentClass)),[n]),y=n?.componentFromFramework,m=n?.componentClass;return a.createElement("div",{ref:w,style:i,className:"ag-header-cell","col-id":o,role:"columnheader"},a.createElement("div",{ref:p,className:"ag-header-cell-resize",role:"presentation"}),a.createElement("div",{ref:E,className:"ag-header-cell-comp-wrapper",role:"presentation"},y?R?a.createElement(m,{...n.params}):a.createElement(m,{...n.params,ref:v}):null))},es=t.memo(Xt),ts=class{constructor(e,s){this.floatingFilterParams=e,this.refreshProps=s}getProps(){return this.floatingFilterParams}refresh(e){this.floatingFilterParams=e,this.refreshProps()}setMethods(e){Pe(this.getOptionalMethods(),e,this)}getOptionalMethods(){return["afterGuiAttached"]}},ss=({ctrl:e})=>{const{context:s,gos:r}=t.useContext(ee),[o,n]=t.useState(),[u,i]=t.useState(()=>new ae("ag-header-cell","ag-floating-filter")),[c,l]=t.useState(()=>new ae),[d,p]=t.useState(()=>new ae("ag-floating-filter-button","ag-hidden")),[E,v]=t.useState("false"),[h,w]=t.useState(),[,R]=t.useState(1),y=t.useRef(),m=t.useRef(null),f=t.useRef(null),A=t.useRef(null),G=t.useRef(null),g=t.useRef(),W=t.useRef(),D=B=>{B!=null&&g.current&&g.current(B)},V=t.useCallback(B=>{if(m.current=B,y.current=B?s.createBean(new fe):s.destroyBean(y.current),!B)return;W.current=new ce(x=>{g.current=x});const O={toggleCss:(x,z)=>i(T=>T.setClass(x,z)),setUserStyles:x=>n(x),addOrRemoveBodyCssClass:(x,z)=>l(T=>T.setClass(x,z)),setButtonWrapperDisplayed:x=>{p(z=>z.setClass("ag-hidden",!x)),v(x?"false":"true")},setWidth:x=>{m.current&&(m.current.style.width=x)},setCompDetails:x=>w(x),getFloatingFilterComp:()=>W.current?W.current:null,setMenuIcon:x=>G.current?.appendChild(x)};e.setComp(O,B,G.current,f.current,y.current)},[]);t.useLayoutEffect(()=>Re(h,s,f.current,D),[h]);const N=t.useMemo(()=>u.toString(),[u]),F=t.useMemo(()=>c.toString(),[c]),b=t.useMemo(()=>d.toString(),[d]),C=t.useMemo(()=>!!(h&&h.componentFromFramework&&Ce(h.componentClass)),[h]),S=t.useMemo(()=>r.get("reactiveCustomComponents"),[]),L=t.useMemo(()=>r.get("enableFilterHandlers"),[]),P=t.useRef();t.useEffect(()=>{if(h?.componentFromFramework)if(S){const B=L?ts:_t,O=new B(h.params,()=>R(x=>x+1));D(O),P.current=O}else Ae()},[h]);const H=P.current?.getProps(),U=h?.componentFromFramework,k=h?.componentClass;return a.createElement("div",{ref:V,style:o,className:N,role:"gridcell"},a.createElement("div",{ref:f,className:F,role:"presentation"},U?S?H&&a.createElement(Ge.Provider,{value:{setMethods:B=>P.current.setMethods(B)}},a.createElement(k,{...H})):a.createElement(k,{...h.params,ref:C?()=>{}:D}):null),a.createElement("div",{ref:A,"aria-hidden":E,className:b,role:"presentation"},a.createElement("button",{ref:G,type:"button",className:"ag-button ag-floating-filter-button-button",tabIndex:-1})))},rs=t.memo(ss),ns=({ctrl:e})=>{const{context:s}=t.useContext(ee),[r,o]=t.useState(),[n,u]=t.useState(()=>new ae),[i,c]=t.useState(()=>new ae),[l,d]=t.useState("false"),[p,E]=t.useState(),[v,h]=t.useState(),w=t.useMemo(()=>e.column.getUniqueId(),[]),R=t.useRef(),y=t.useRef(null),m=t.useRef(null),f=t.useRef(null),A=t.useRef(),G=t.useCallback(F=>{if(y.current=F,R.current=F?s.createBean(new fe):s.destroyBean(R.current),!F)return;const b={setWidth:C=>{y.current&&(y.current.style.width=C)},toggleCss:(C,S)=>u(L=>L.setClass(C,S)),setUserStyles:C=>o(C),setHeaderWrapperHidden:C=>{const S=f.current;S&&(C?S.style.setProperty("display","none"):S.style.removeProperty("display"))},setHeaderWrapperMaxHeight:C=>{const S=f.current;S&&(C!=null?S.style.setProperty("max-height",`${C}px`):S.style.removeProperty("max-height"),S.classList.toggle("ag-header-cell-comp-wrapper-limited-height",C!=null))},setUserCompDetails:C=>h(C),setResizableDisplayed:C=>{c(S=>S.setClass("ag-hidden",!C)),d(C?"false":"true")},setAriaExpanded:C=>E(C),getUserCompInstance:()=>A.current||void 0};e.setComp(b,F,m.current,f.current,R.current)},[]);t.useLayoutEffect(()=>Re(v,s,f.current),[v]),t.useEffect(()=>{y.current&&e.setDragSource(y.current)},[v]);const g=t.useMemo(()=>!!(v?.componentFromFramework&&Ce(v.componentClass)),[v]),W=t.useMemo(()=>"ag-header-group-cell "+n.toString(),[n]),D=t.useMemo(()=>"ag-header-cell-resize "+i.toString(),[i]),V=v?.componentFromFramework,N=v?.componentClass;return a.createElement("div",{ref:G,style:r,className:W,"col-id":w,role:"columnheader","aria-expanded":p},a.createElement("div",{ref:f,className:"ag-header-cell-comp-wrapper",role:"presentation"},V?g?a.createElement(N,{...v.params}):a.createElement(N,{...v.params,ref:A}):null),a.createElement("div",{ref:m,"aria-hidden":l,className:D}))},os=t.memo(ns),as=({ctrl:e})=>{const{context:s}=t.useContext(ee),{topOffset:r,rowHeight:o}=t.useMemo(()=>e.getTopAndHeight(),[]),n=e.getAriaRowIndex(),u=e.headerRowClass,[i,c]=t.useState(()=>o+"px"),[l,d]=t.useState(()=>r+"px"),p=t.useRef([]),[E,v]=t.useState(()=>e.getUpdatedHeaderCtrls()),h=t.useRef(),w=t.useRef(null),R=t.useCallback(f=>{if(w.current=f,h.current=f?s.createBean(new fe):s.destroyBean(h.current),!f)return;const A={setHeight:G=>c(G),setTop:G=>d(G),setHeaderCtrls:(G,g,W)=>{const D=p.current,V=be(D,G,g);V!==D&&(p.current=V,ve(W,()=>v(V)))},setWidth:G=>{w.current&&(w.current.style.width=G)}};e.setComp(A,h.current,!1)},[]),y=t.useMemo(()=>({height:i,top:l}),[i,l]),m=t.useCallback(f=>{switch(e.type){case"group":return a.createElement(os,{ctrl:f,key:f.instanceId});case"filter":return a.createElement(rs,{ctrl:f,key:f.instanceId});default:return a.createElement(es,{ctrl:f,key:f.instanceId})}},[]);return a.createElement("div",{ref:R,className:u,role:"row",style:y,"aria-rowindex":n},E.map(m))},cs=t.memo(as),ls=({pinned:e})=>{const[s,r]=t.useState(!0),[o,n]=t.useState([]),{context:u}=t.useContext(ee),i=t.useRef(null),c=t.useRef(null),l=t.useRef(),d=e==="left",p=e==="right",E=!d&&!p,v=t.useCallback(R=>{if(i.current=R,l.current=R?u.createBean(new vt(e)):u.destroyBean(l.current),!R)return;const y={setDisplayed:r,setCtrls:m=>n(m),setCenterWidth:m=>{c.current&&(c.current.style.width=m)},setViewportScrollLeft:m=>{i.current&&(i.current.scrollLeft=m)},setPinnedContainerWidth:m=>{i.current&&(i.current.style.width=m,i.current.style.minWidth=m,i.current.style.maxWidth=m)}};l.current.setComp(y,i.current)},[]),h=s?"":"ag-hidden",w=()=>o.map(R=>a.createElement(cs,{ctrl:R,key:R.instanceId}));return d?a.createElement("div",{ref:v,className:"ag-pinned-left-header "+h,"aria-hidden":!s,role:"rowgroup"},w()):p?a.createElement("div",{ref:v,className:"ag-pinned-right-header "+h,"aria-hidden":!s,role:"rowgroup"},w()):E?a.createElement("div",{ref:v,className:"ag-header-viewport "+h,role:"presentation",tabIndex:-1},a.createElement("div",{ref:c,className:"ag-header-container",role:"rowgroup"},w())):null},Me=t.memo(ls),is=()=>{const[e,s]=t.useState(()=>new ae),[r,o]=t.useState(),{context:n}=t.useContext(ee),u=t.useRef(null),i=t.useRef(),c=t.useCallback(p=>{if(u.current=p,i.current=p?n.createBean(new yt):n.destroyBean(i.current),!p)return;const E={toggleCss:(v,h)=>s(w=>w.setClass(v,h)),setHeightAndMinHeight:v=>o(v)};i.current.setComp(E,p,p)},[]),l=t.useMemo(()=>"ag-header "+e.toString(),[e]),d=t.useMemo(()=>({height:r,minHeight:r}),[r]);return a.createElement("div",{ref:c,className:l,style:d,role:"presentation"},a.createElement(Me,{pinned:"left"}),a.createElement(Me,{pinned:null}),a.createElement(Me,{pinned:"right"}))},us=t.memo(is),ds=(e,s)=>{t.useEffect(()=>{const r=s.current;if(r){const o=r.parentElement;if(o){const n=document.createComment(e);return o.insertBefore(n,r),()=>{o.removeChild(n)}}}},[e])},me=ds,ps=class{constructor(e,s){this.cellEditorParams=e,this.refreshProps=s,this.instanceCreated=new ce(r=>{this.resolveInstanceCreated=r}),this.onValueChange=r=>this.updateValue(r),this.value=e.value}getProps(){return{...this.cellEditorParams,initialValue:this.cellEditorParams.value,value:this.value,onValueChange:this.onValueChange}}getValue(){return this.value}refresh(e){this.cellEditorParams=e,this.refreshProps()}setMethods(e){Pe(this.getOptionalMethods(),e,this)}getInstance(){return this.instanceCreated.then(()=>this.componentInstance)}setRef(e){this.componentInstance=e,this.resolveInstanceCreated?.(),this.resolveInstanceCreated=void 0}getOptionalMethods(){return["isCancelBeforeStart","isCancelAfterEnd","focusIn","focusOut","afterGuiAttached","getValidationErrors","getValidationElement"]}updateValue(e){this.value=e,this.refreshProps()}},ms=e=>{const s=t.useRef(e),r=t.useRef(),o=t.useRef(!1),n=t.useRef(!1),[,u]=t.useState(0);o.current&&(n.current=!0),t.useEffect(()=>(o.current||(r.current=s.current(),o.current=!0),u(i=>i+1),()=>{n.current&&r.current?.()}),[])},hs=e=>{const[s,r]=t.useState(),o=t.useContext(ee),{context:n,popupSvc:u,localeSvc:i,gos:c,editSvc:l}=o,{editDetails:d,cellCtrl:p,eParentCell:E}=e;return ms(()=>{const{compDetails:v}=d,h=c.get("stopEditingWhenCellsLoseFocus"),w=n.createBean(l.createPopupEditorWrapper(v.params)),R=w.getGui();if(e.jsChildComp){const D=e.jsChildComp.getGui();D&&R.appendChild(D)}const{column:y,rowNode:m}=p,f={column:y,rowNode:m,type:"popupCellEditor",eventSource:E,ePopup:R,position:d.popupPosition,keepWithinBounds:!0},A=u?.positionPopupByComponent.bind(u,f),G=xt(i),g=u?.addPopup({modal:h,eChild:R,closeOnEsc:!0,closedCallback:()=>{p.onPopupEditorClosed()},anchorToElement:E,positionCallback:A,ariaLabel:G("ariaLabelCellEditor","Cell Editor")}),W=g?g.hideFunc:void 0;return r(w),e.jsChildComp?.afterGuiAttached?.(),()=>{W?.(),n.destroyBean(w)}}),t.useLayoutEffect(()=>()=>{p.isCellFocused()&&s?.getGui().contains(Ft(o))&&E.focus({preventScroll:!0})},[s]),s&&e.wrappedContent?Ve.createPortal(e.wrappedContent,s.getGui()):null},Te=t.memo(hs),fs=(e,s,r)=>{const{compProxy:o}=e;r(o);const n=o.getProps(),u=Ce(s);return a.createElement(Ge.Provider,{value:{setMethods:i=>o.setMethods(i)}},u?a.createElement(s,{...n}):a.createElement(s,{...n,ref:i=>o.setRef(i)}))},Ne=(e,s,r)=>e.compProxy?fs(e,s,r):a.createElement(s,{...e.compDetails.params,ref:r}),Cs=(e,s,r,o,n)=>{const u=e.compDetails,i=u.componentClass,c=u.componentFromFramework&&!e.popup,l=u.componentFromFramework&&e.popup,d=!u.componentFromFramework&&e.popup;return c?Ne(e,i,s):l?a.createElement(Te,{editDetails:e,cellCtrl:o,eParentCell:r,wrappedContent:Ne(e,i,s)}):d&&n?a.createElement(Te,{editDetails:e,cellCtrl:o,eParentCell:r,jsChildComp:n}):null},gs=(e,s,r,o,n,u)=>{const{context:i}=t.useContext(ee),c=t.useCallback(()=>{const l=n.current;if(!l)return;const d=l.getGui();d&&d.parentElement&&d.parentElement.removeChild(d),i.destroyBean(l),n.current=void 0},[]);t.useEffect(()=>{const l=e!=null,d=e?.compDetails&&!e.compDetails.componentFromFramework,p=s&&r==null;if(!(l&&d&&!p)){c();return}const v=e.compDetails;if(n.current){const w=n.current,y=w.refresh!=null&&e.force==!1?w.refresh(v.params):!1;if(y===!0||y===void 0)return;c()}v.newAgStackInstance().then(w=>{if(!w)return;const R=w.getGui();if(!R)return;(s?r:u.current).appendChild(R),n.current=w})},[e,s,o]),t.useEffect(()=>c,[])},qe=gs,ys=({cellCtrl:e,parent:s})=>{const r=t.useRef(),o=t.useMemo(()=>{const{loadingComp:n}=e.getDeferLoadingCellRenderer();return n?{value:void 0,compDetails:n,force:!1}:void 0},[e]);if(qe(o,!1,void 0,1,r,s),o?.compDetails?.componentFromFramework){const n=o.compDetails.componentClass;return a.createElement(n,{...o.compDetails.params})}return a.createElement(a.Fragment,null)},vs=({cellCtrl:e,printLayout:s,editingCell:r})=>{const o=t.useContext(ee),{context:n}=o,{column:{colIdSanitised:u},instanceId:i}=e,c=t.useRef(),[l,d]=t.useState(()=>e.isCellRenderer()?void 0:{compDetails:void 0,value:e.getValueToDisplay(),force:!1}),[p,E]=t.useState(),[v,h]=t.useState(1),[w,R]=t.useState(),[y,m]=t.useState(!1),[f,A]=t.useState(!1),[G,g]=t.useState(!1),[W,D]=t.useState(),V=t.useMemo(()=>e.isForceWrapper(),[e]),N=t.useMemo(()=>e.getCellAriaRole(),[e]),F=t.useRef(null),b=t.useRef(null),C=t.useRef(null),S=t.useRef(),L=t.useRef(),P=t.useRef(),H=t.useRef([]),U=t.useRef(),[k,B]=t.useState(0),O=t.useCallback(M=>{U.current=M,B(_=>_+1)},[]),x=l!=null&&(y||G||f)&&(p==null||!!p.popup),z=V||x,T=t.useCallback(M=>{if(L.current=M,M){const _=M.isCancelBeforeStart&&M.isCancelBeforeStart();setTimeout(()=>{_?(e.stopEditing(!0),e.focusCell(!0)):(e.cellEditorAttached(),e.enableEditorTooltipFeature(M))})}},[e]),q=t.useRef();q.current||(q.current=new Ee(()=>F.current)),qe(l,z,U.current,k,S,F);const Q=t.useRef();t.useLayoutEffect(()=>{const M=Q.current,_=l;if(Q.current=l,M==null||M.compDetails==null||_==null||_.compDetails==null)return;const J=M.compDetails,ne=_.compDetails;if(J.componentClass!=ne.componentClass||C.current?.refresh==null)return;C.current.refresh(ne.params)!=!0&&h(K=>K+1)},[l]),t.useLayoutEffect(()=>{if(!(p&&!p.compDetails.componentFromFramework))return;const _=p.compDetails,J=p.popup===!0,ne=_.newAgStackInstance();return ne.then(I=>{if(!I)return;const K=I.getGui();T(I),J||((V?P:F).current?.appendChild(K),I.afterGuiAttached&&I.afterGuiAttached()),D(I)}),()=>{ne.then(I=>{const K=I.getGui();e.disableEditorTooltipFeature(),n.destroyBean(I),T(void 0),D(void 0),K?.parentElement?.removeChild(K)})}},[p]);const ie=t.useCallback(M=>{if(P.current=M,!M){H.current.forEach(J=>J()),H.current=[];return}const _=J=>{if(J){const ne=J.getGui();M.insertAdjacentElement("afterbegin",ne),H.current.push(()=>{n.destroyBean(J),Mt(ne)})}return J};if(y){const J=e.createSelectionCheckbox();_(J)}G&&_(e.createDndSource()),f&&_(e.createRowDragComp())},[e,n,G,f,y]),he=t.useCallback(()=>{const M=!e.isCellSpanning()||b.current,_=F.current;if(c.current=_?n.createBean(new fe):n.destroyBean(c.current),!_||!M||!e)return;const J={toggleCss:(I,K)=>q.current.toggleCss(I,K),setUserStyles:I=>R(I),getFocusableElement:()=>F.current,setIncludeSelection:I=>m(I),setIncludeRowDrag:I=>A(I),setIncludeDndSource:I=>g(I),getCellEditor:()=>L.current||null,getCellRenderer:()=>C.current??S.current,getParentOfValue:()=>U.current??P.current??F.current,setRenderDetails:(I,K,Y)=>{const Z=()=>{d(X=>X?.compDetails!==I||X?.value!==K||X?.force!==Y?{value:K,compDetails:I,force:Y}:X)};if(I?.params?.deferRender&&!e.rowNode.group){const{loadingComp:X,onReady:ue}=e.getDeferLoadingCellRenderer();if(X){d({value:void 0,compDetails:X,force:!1}),ue.then(()=>Bt(Z));return}}Z()},setEditDetails:(I,K,Y,Z)=>{if(I){let X;Z?X=new ps(I.params,()=>h(ue=>ue+1)):I.componentFromFramework&&Ae(),E({compDetails:I,popup:K,popupPosition:Y,compProxy:X}),K||d(void 0)}else e.hasBrowserFocus()&&J.getFocusableElement().focus({preventScroll:!0}),E(ue=>{ue?.compProxy&&(L.current=void 0)})},refreshEditStyles:(I,K)=>{if(!F.current)return;const{current:Y}=q;Y.toggleCss("ag-cell-value",!z),Y.toggleCss("ag-cell-inline-editing",!!I&&!K),Y.toggleCss("ag-cell-popup-editing",!!I&&!!K),Y.toggleCss("ag-cell-not-inline-editing",!I||!!K)}},ne=P.current||void 0;e.setComp(J,_,b.current??void 0,ne,s,r,c.current)},[]),ge=t.useCallback(M=>{F.current=M,he()},[]),j=t.useCallback(M=>{b.current=M,he()},[]),de=t.useMemo(()=>!!(l?.compDetails?.componentFromFramework&&Ce(l.compDetails.componentClass)),[l]);t.useLayoutEffect(()=>{if(!F.current)return;const{current:M}=q;M.toggleCss("ag-cell-value",!z),M.toggleCss("ag-cell-inline-editing",!!p&&!p.popup),M.toggleCss("ag-cell-popup-editing",!!p&&!!p.popup),M.toggleCss("ag-cell-not-inline-editing",!p||!!p.popup)});const te=()=>{const{compDetails:M,value:_}=l;if(!M)return _?.toString?.()??_;if(M.componentFromFramework){const J=M.componentClass;return a.createElement(t.Suspense,{fallback:a.createElement(ys,{cellCtrl:e,parent:F})},de?a.createElement(J,{...M.params,key:v}):a.createElement(J,{...M.params,key:v,ref:C}))}},le=()=>{const M=()=>l==null?null:z?a.createElement("span",{role:"presentation",id:`cell-${i}`,className:"ag-cell-value",ref:O},te()):te(),_=J=>Cs(J,T,F.current,e,W);return p!=null?p.popup?a.createElement(a.Fragment,null,M(),_(p)):_(p):M()},pe=()=>a.createElement("div",{ref:ge,style:w,role:N,"col-id":u},z?a.createElement("div",{className:"ag-cell-wrapper",role:"presentation",ref:ie},le()):le());return e.isCellSpanning()?a.createElement("div",{ref:j,className:"ag-spanned-cell-wrapper",role:"presentation"},pe()):pe()},Rs=t.memo(vs),Ss=({rowCtrl:e,containerType:s})=>{const{context:r,gos:o,editSvc:n}=t.useContext(ee),u=t.useContext(_e)==="default",i=t.useRef(),c=t.useRef(e.getDomOrder()),l=e.isFullWidth(),d=e.rowNode.displayed,[p,E]=t.useState(()=>d?e.rowNode.getRowIndexString():null),[v,h]=t.useState(()=>e.rowId),[w,R]=t.useState(()=>e.businessKey),[y,m]=t.useState(()=>e.rowStyles),f=t.useRef(null),[A,G]=t.useState(()=>null),[g,W]=t.useState(),[D,V]=t.useState(()=>d?e.getInitialRowTop(s):void 0),[N,F]=t.useState(()=>d?e.getInitialTransform(s):void 0),b=t.useRef(null),C=t.useRef(),S=t.useRef(!1),[L,P]=t.useState(0);t.useEffect(()=>{if(S.current||!g||L>10)return;const j=b.current?.firstChild;j?(e.setupDetailRowAutoHeight(j),S.current=!0):P(de=>de+1)},[g,L]);const H=t.useRef();H.current||(H.current=new Ee(()=>b.current));const U=t.useRef(()=>{}),k=t.useCallback(j=>(U.current=j,()=>{U.current=()=>{}}),[]),B=Dt(k,()=>f.current,[]),O=u?B:A,x=t.useCallback(j=>{if(b.current=j,i.current=j?r.createBean(new fe):r.destroyBean(i.current),!j){e.unsetComp(s);return}if(!e.isAlive())return;const de={setTop:V,setTransform:F,toggleCss:(te,le)=>H.current.toggleCss(te,le),setDomOrder:te=>c.current=te,setRowIndex:E,setRowId:h,setRowBusinessKey:R,setUserStyles:m,setCellCtrls:(te,le)=>{const pe=f.current,M=be(pe,te,c.current);M!==pe&&(f.current=M,u?U.current():ve(le,()=>G(M)))},showFullWidth:te=>W(te),getFullWidthCellRenderer:()=>C.current,refreshFullWidth:te=>ie.current?(W(le=>({...le,params:te()})),!0):!C.current||!C.current.refresh?!1:C.current.refresh(te())};e.setComp(de,j,s,i.current)},[]);t.useLayoutEffect(()=>Re(g,r,b.current,C),[g]);const z=t.useMemo(()=>{const j={top:D,transform:N};return Object.assign(j,y),j},[D,N,y]),T=l&&g?.componentFromFramework,q=!l&&O!=null,Q=t.useMemo(()=>!!(g?.componentFromFramework&&Ce(g.componentClass)),[g]),ie=t.useRef(!1);t.useEffect(()=>{ie.current=Q&&!!g&&!!o.get("reactiveCustomComponents")},[Q,g]);const he=()=>O?.map(j=>a.createElement(Rs,{cellCtrl:j,editingCell:n?.isEditing(j,{withOpenEditor:!0})??!1,printLayout:e.printLayout,key:j.instanceId})),ge=()=>{const j=g.componentClass;return Q?a.createElement(j,{...g.params}):a.createElement(j,{...g.params,ref:C})};return a.createElement("div",{ref:x,role:"row",style:z,"row-index":p,"row-id":v,"row-business-key":w},q?he():T?ge():null)},He=t.memo(Ss),ws=({name:e})=>{const{context:s,gos:r}=t.useContext(ee),o=t.useMemo(()=>Rt(e),[e]),n=t.useRef(null),u=t.useRef(null),i=t.useRef(null),c=t.useRef([]),l=t.useRef([]),[d,p]=t.useState(()=>[]),E=!!r.get("enableCellSpan")&&!!o.getSpannedRowCtrls,v=t.useRef([]),h=t.useRef([]),[w,R]=t.useState(()=>[]),y=t.useRef(!1),m=t.useRef(),f=t.useMemo(()=>oe("ag-viewport",St(e)),[e]),A=t.useMemo(()=>oe(wt(e)),[e]),G=t.useMemo(()=>oe("ag-spanning-container",bt(e)),[e]),g=o.type==="center"||E,W=g?n:u;me(" AG Row Container "+e+" ",W);const D=t.useCallback(()=>{const P=!g||n.current!=null,H=u.current!=null,U=!E||i.current!=null;return P&&H&&U},[]),V=t.useCallback(()=>n.current==null&&u.current==null&&i.current==null,[]),N=t.useCallback(()=>{if(V()&&(m.current=s.destroyBean(m.current)),D()){const P=k=>{const B=be(l.current,c.current,y.current);B!==l.current&&(l.current=B,ve(k,()=>p(B)))},H=k=>{const B=be(h.current,v.current,y.current);B!==h.current&&(h.current=B,ve(k,()=>R(B)))},U={setHorizontalScroll:k=>{n.current&&(n.current.scrollLeft=k)},setViewportHeight:k=>{n.current&&(n.current.style.height=k)},setRowCtrls:({rowCtrls:k,useFlushSync:B})=>{const O=!!B&&c.current.length>0&&k.length>0;c.current=k,P(O)},setSpannedRowCtrls:(k,B)=>{const O=!!B&&v.current.length>0&&k.length>0;v.current=k,H(O)},setDomOrder:k=>{y.current!=k&&(y.current=k,P(!1))},setContainerWidth:k=>{u.current&&(u.current.style.width=k)},setOffsetTop:k=>{u.current&&(u.current.style.transform=`translateY(${k})`)}};m.current=s.createBean(new Et(e)),m.current.setComp(U,u.current,i.current??void 0,n.current)}},[D,V]),F=t.useCallback(P=>{u.current=P,N()},[N]),b=t.useCallback(P=>{i.current=P,N()},[N]),C=t.useCallback(P=>{n.current=P,N()},[N]),S=()=>a.createElement("div",{className:A,ref:F,role:"rowgroup"},d.map(P=>a.createElement(He,{rowCtrl:P,containerType:o.type,key:P.instanceId})));if(!g)return S();const L=()=>a.createElement("div",{className:G,ref:b,role:"rowgroup"},w.map(P=>a.createElement(He,{rowCtrl:P,containerType:o.type,key:P.instanceId})));return a.createElement("div",{className:f,ref:C,role:"presentation"},S(),E?L():null)},bs=t.memo(ws),Es=()=>{const e=t.useContext(ee),{context:s,overlays:r}=e,[o,n]=t.useState(""),[u,i]=t.useState(0),[c,l]=t.useState(0),[d,p]=t.useState("0px"),[E,v]=t.useState("0px"),[h,w]=t.useState("100%"),[R,y]=t.useState("0px"),[m,f]=t.useState("0px"),[A,G]=t.useState("100%"),[g,W]=t.useState(!0),[D,V]=t.useState(!0),[N,F]=t.useState(null),[b,C]=t.useState(""),[S,L]=t.useState(null),[P,H]=t.useState("ag-layout-normal"),U=t.useRef();U.current||(U.current=new Ee(()=>k.current));const k=t.useRef(null),B=t.useRef(null),O=t.useRef(null),x=t.useRef(null),z=t.useRef(null),T=t.useRef(null),q=t.useRef(null),Q=t.useRef([]),ie=t.useRef([]);me(" AG Grid Body ",k),me(" AG Pinned Top ",B),me(" AG Sticky Top ",O),me(" AG Middle ",T),me(" AG Pinned Bottom ",q);const he=t.useCallback(Z=>{if(k.current=Z,!Z){Q.current=s.destroyBeans(Q.current),ie.current.forEach($=>$()),ie.current=[];return}if(!s)return;const X=($,se)=>{$.appendChild(se),ie.current.push(()=>$.removeChild(se))},ue=$=>{const se=s.createBean(new $);return Q.current.push(se),se},ye=($,se,Xe)=>{X($,document.createComment(Xe)),X($,ue(se).getGui())};ye(Z,Ct," AG Fake Horizontal Scroll ");const Be=r?.getOverlayWrapperCompClass();Be&&ye(Z,Be," AG Overlay Wrapper "),z.current&&ye(z.current,gt," AG Fake Vertical Scroll ");const Ze={setRowAnimationCssOnBodyViewport:n,setColumnCount:$=>{k.current&&ft(k.current,$)},setRowCount:$=>{k.current&&ht(k.current,$)},setTopHeight:i,setBottomHeight:l,setStickyTopHeight:p,setStickyTopTop:v,setStickyTopWidth:w,setTopInvisible:W,setBottomInvisible:V,setColumnMovingCss:($,se)=>U.current.toggleCss($,se),updateLayoutClasses:H,setAlwaysVerticalScrollClass:F,setPinnedTopBottomOverflowY:C,setCellSelectableCss:($,se)=>L(se?$:null),setBodyViewportWidth:$=>{T.current&&(T.current.style.width=$)},registerBodyViewportResizeListener:$=>{if(T.current){const se=Ue(e,T.current,$);ie.current.push(()=>se())}},setStickyBottomHeight:y,setStickyBottomBottom:f,setStickyBottomWidth:G,setGridRootRole:$=>Z.setAttribute("role",$)},De=s.createBean(new pt);Q.current.push(De),De.setComp(Ze,Z,T.current,B.current,q.current,O.current,x.current)},[]),ge=t.useMemo(()=>oe("ag-root","ag-unselectable",P),[P]),j=t.useMemo(()=>oe("ag-body-viewport",o,P,N,S),[o,P,N,S]),de=t.useMemo(()=>oe("ag-body",P),[P]),te=t.useMemo(()=>oe("ag-floating-top",g?"ag-invisible":null,S),[S,g]),le=t.useMemo(()=>oe("ag-sticky-top",S),[S]),pe=t.useMemo(()=>oe("ag-sticky-bottom",R==="0px"?"ag-invisible":null,S),[S,R]),M=t.useMemo(()=>oe("ag-floating-bottom",D?"ag-invisible":null,S),[S,D]),_=t.useMemo(()=>({height:u,minHeight:u,overflowY:b}),[u,b]),J=t.useMemo(()=>({height:d,top:E,width:h}),[d,E,h]),ne=t.useMemo(()=>({height:R,bottom:m,width:A}),[R,m,A]),I=t.useMemo(()=>({height:c,minHeight:c,overflowY:b}),[c,b]),K=Z=>a.createElement(bs,{name:Z,key:`${Z}-container`}),Y=({section:Z,children:X,className:ue,style:ye})=>a.createElement("div",{ref:Z,className:ue,role:"presentation",style:ye},X.map(K));return a.createElement("div",{ref:he,className:ge},a.createElement(us,null),Y({section:B,className:te,style:_,children:["topLeft","topCenter","topRight","topFullWidth"]}),a.createElement("div",{className:de,ref:z,role:"presentation"},Y({section:T,className:j,children:["left","center","right","fullWidth"]})),Y({section:O,className:le,style:J,children:["stickyTopLeft","stickyTopCenter","stickyTopRight","stickyTopFullWidth"]}),Y({section:x,className:pe,style:ne,children:["stickyBottomLeft","stickyBottomCenter","stickyBottomRight","stickyBottomFullWidth"]}),Y({section:q,className:M,style:I,children:["bottomLeft","bottomCenter","bottomRight","bottomFullWidth"]}))},Ps=t.memo(Es),ks=(e,s)=>{const{children:r,eFocusableElement:o,onTabKeyDown:n,gridCtrl:u,forceFocusOutWhenTabGuardsAreEmpty:i,isEmpty:c}=e,{context:l}=t.useContext(ee),d=t.useRef(null),p=t.useRef(null),E=t.useRef(),v=m=>{const f=m==null?void 0:parseInt(m,10).toString();[d,p].forEach(A=>{f===void 0?A.current?.removeAttribute("tabindex"):A.current?.setAttribute("tabindex",f)})};t.useImperativeHandle(s,()=>({forceFocusOutOfContainer(m){E.current?.forceFocusOutOfContainer(m)}}));const h=t.useCallback(()=>{const m=d.current,f=p.current;if(!m&&!f){E.current=l.destroyBean(E.current);return}if(m&&f){const A={setTabIndex:v};E.current=l.createBean(new mt({comp:A,eTopGuard:m,eBottomGuard:f,eFocusableElement:o,onTabKeyDown:n,forceFocusOutWhenTabGuardsAreEmpty:i,focusInnerElement:G=>u.focusInnerElement(G),isEmpty:c}))}},[]),w=t.useCallback(m=>{d.current=m,h()},[h]),R=t.useCallback(m=>{p.current=m,h()},[h]),y=m=>{const f=m==="top"?ke.TAB_GUARD_TOP:ke.TAB_GUARD_BOTTOM;return a.createElement("div",{className:`${ke.TAB_GUARD} ${f}`,role:"presentation",ref:m==="top"?w:R})};return a.createElement(a.Fragment,null,y("top"),r,y("bottom"))},Ms=t.forwardRef(ks),Fs=t.memo(Ms),xs=({context:e})=>{const[s,r]=t.useState(""),[o,n]=t.useState(""),[u,i]=t.useState(null),[c,l]=t.useState(null),[d,p]=t.useState(!1),[E,v]=t.useState(),h=t.useRef(),w=t.useRef(null),R=t.useRef(),[y,m]=t.useState(null),f=t.useRef(()=>{}),A=t.useRef(),G=t.useRef([]),g=t.useCallback(()=>{},[]),W=t.useMemo(()=>e.isDestroyed()?null:e.getBeans(),[e]);me(" AG Grid ",w);const D=t.useCallback(S=>{if(w.current=S,h.current=S?e.createBean(new it):e.destroyBean(h.current),!S||e.isDestroyed())return;const L=h.current;f.current=L.focusInnerElement.bind(L);const P={destroyGridUi:()=>{},setRtlClass:r,forceFocusOutOfContainer:H=>{if(!H&&A.current?.isDisplayed()){A.current.forceFocusOutOfContainer(H);return}R.current?.forceFocusOutOfContainer(H)},updateLayoutClasses:n,getFocusableContainers:()=>{const H=[],U=w.current?.querySelector(".ag-root");return U&&H.push({getGui:()=>U}),G.current.forEach(k=>{k.isDisplayed()&&H.push(k)}),H},setCursor:i,setUserSelect:l};L.setComp(P,S,S),p(!0)},[]);t.useEffect(()=>{const S=h.current,L=w.current;if(!E||!W||!S||!y||!L)return;const P=[],{watermarkSelector:H,paginationSelector:U,sideBarSelector:k,statusBarSelector:B,gridHeaderDropZonesSelector:O}=S.getOptionalSelectors(),x=[];if(O){const T=e.createBean(new O.component),q=T.getGui();L.insertAdjacentElement("afterbegin",q),x.push(q),P.push(T)}if(k){const T=e.createBean(new k.component),q=T.getGui(),Q=y.querySelector(".ag-tab-guard-bottom");Q&&(Q.insertAdjacentElement("beforebegin",q),x.push(q)),P.push(T),G.current.push(T)}const z=T=>{const q=e.createBean(new T),Q=q.getGui();return L.insertAdjacentElement("beforeend",Q),x.push(Q),P.push(q),q};if(B&&z(B.component),U){const T=z(U.component);A.current=T,G.current.push(T)}return H&&z(H.component),()=>{e.destroyBeans(P),x.forEach(T=>{T.parentElement?.removeChild(T)})}},[E,y,W]);const V=t.useMemo(()=>oe("ag-root-wrapper",s,o),[s,o]),N=t.useMemo(()=>oe("ag-root-wrapper-body","ag-focus-managed",o),[o]),F=t.useMemo(()=>({userSelect:c??"",WebkitUserSelect:c??"",cursor:u??""}),[c,u]),b=t.useCallback(S=>{R.current=S,v(S!==null)},[]),C=t.useCallback(()=>!h.current?.isFocusable(),[]);return a.createElement("div",{ref:D,className:V,style:F,role:"presentation"},a.createElement("div",{className:N,ref:m,role:"presentation"},d&&y&&W&&a.createElement(ee.Provider,{value:W},a.createElement(Fs,{ref:b,eFocusableElement:y,onTabKeyDown:g,gridCtrl:h.current,forceFocusOutWhenTabGuardsAreEmpty:!0,isEmpty:C},a.createElement(Ps,null)))))},Gs=t.memo(xs),As=class extends ot{wireBeans(e){this.ctrlsSvc=e.ctrlsSvc}areHeaderCellsRendered(){return this.ctrlsSvc.getHeaderRowContainerCtrls().every(e=>e.getAllCtrls().every(s=>s.areCellsRendered()))}},Ke={setGridApi:void 0,maxComponentCreationTimeMs:void 0,children:void 0},Bs={gridOptions:void 0,modules:void 0,containerStyle:void 0,className:void 0,passGridApi:void 0,componentWrappingElement:void 0,...Ke},Qe=new Set(Object.keys(Bs)),Ds=new Set(Object.keys(Ke)),Ye=e=>{const s=t.useRef(),r=t.useRef(null),o=t.useRef(null),n=t.useRef([]),u=t.useRef([]),i=t.useRef(e),c=t.useRef(),l=t.useRef(),d=t.useRef(!1),[p,E]=t.useState(void 0),[,v]=t.useState(0),h=t.useCallback(m=>{if(r.current=m,!m){n.current.forEach(b=>b()),n.current.length=0;return}const f=e.modules||[];o.current||(o.current=new Zt(()=>v(b=>b+1),e.componentWrappingElement,e.maxComponentCreationTimeMs),n.current.push(()=>{o.current?.destroy(),o.current=null}));const A=et(e.gridOptions,e,Object.keys(e).filter(b=>!Qe.has(b))),G=()=>{if(d.current){const b=()=>c.current?.shouldQueueUpdates()?void 0:u.current.shift();let C=b();for(;C;)C(),C=b()}},g=new Ts(G);c.current=g;const W=new As,D={providedBeanInstances:{frameworkCompWrapper:new Os(o.current,A),renderStatus:W},modules:f,frameworkOverrides:g,setThemeOnGridDiv:!0},V=b=>{E(b),b.createBean(W),n.current.push(()=>{b.destroy()}),b.getBean("ctrlsSvc").whenReady({addDestroyFunc:C=>{n.current.push(C)}},()=>{if(b.isDestroyed())return;const C=s.current;C&&e.passGridApi?.(C)})},N=b=>{b.getBean("ctrlsSvc").whenReady({addDestroyFunc:C=>{n.current.push(C)}},()=>{u.current.forEach(C=>C()),u.current.length=0,d.current=!0})},F=new tt;A.gridId??(A.gridId=l.current),s.current=F.create(m,A,V,N,D),n.current.push(()=>{s.current=void 0}),s.current&&(l.current=s.current.getGridId())},[]),w=t.useMemo(()=>({height:"100%",...e.containerStyle||{}}),[e.containerStyle]),R=t.useCallback(m=>{d.current&&!c.current?.shouldQueueUpdates()?m():u.current.push(m)},[]);t.useEffect(()=>{const m=Is(i.current,e);i.current=e,R(()=>{s.current&&st(m,s.current)})},[e]);const y=!a.useSyncExternalStore||Se(e,"renderingMode")==="legacy"?"legacy":"default";return a.createElement("div",{style:w,className:e.className,ref:h},a.createElement(_e.Provider,{value:y},p&&!p.isDestroyed()?a.createElement(Gs,{context:p}):null,o.current?.getPortals()??null))};function Is(e,s){const r={};return Object.keys(s).forEach(o=>{if(Qe.has(o)){Ds.has(o)&&xe(274,{prop:o});return}const n=s[o];e[o]!==n&&(r[o]=n)}),r}var Os=class extends nt{constructor(e,s){super(),this.parent=e,this.gridOptions=s}createWrapper(e,s){const r=this.gridOptions;if(Se(r,"reactiveCustomComponents")){const i=(c=>{switch(c){case"filter":return Se(r,"enableFilterHandlers")?Vt:Ut;case"floatingFilterComponent":return Se(r,"enableFilterHandlers")?jt:Lt;case"dateComponent":return Nt;case"dragAndDropImageComponent":return Ht;case"loadingOverlayComponent":return $t;case"noRowsOverlayComponent":return qt;case"statusPanel":return Kt;case"toolPanel":return Qt;case"menuItem":return Jt;case"cellRenderer":return Tt;case"innerHeaderComponent":return zt}})(s.name);if(i)return new i(e,this.parent,s)}else switch(s.name){case"filter":case"floatingFilterComponent":case"dateComponent":case"dragAndDropImageComponent":case"loadingOverlayComponent":case"noRowsOverlayComponent":case"statusPanel":case"toolPanel":case"menuItem":case"cellRenderer":Ae();break}const n=!s.cellRenderer&&s.name!=="toolPanel";return new $e(e,this.parent,s,n)}},Ws=t.forwardRef((e,s)=>{const r=t.useContext(ee),{registry:o,context:n,gos:u,rowModel:i}=r,[c,l]=t.useState(()=>new ae),[d,p]=t.useState(()=>new ae),[E,v]=t.useState(),[h,w]=t.useState(),R=t.useRef(),y=t.useRef(null),m=t.useRef(),f=t.useMemo(()=>ct(e.api.getGridId(),E?.rowModelType??"clientSide"),[e]),A=t.useMemo(()=>c.toString()+" ag-details-row",[c]),G=t.useMemo(()=>d.toString()+" ag-details-grid",[d]);s&&t.useImperativeHandle(s,()=>({refresh(){return R.current?.refresh()??!1}})),e.template&&xe(230);const g=t.useCallback(D=>{if(y.current=D,!D){R.current=n.destroyBean(R.current),m.current?.();return}const V={toggleCss:(F,b)=>l(C=>C.setClass(F,b)),toggleDetailGridCss:(F,b)=>p(C=>C.setClass(F,b)),setDetailGrid:F=>v(F),setRowData:F=>w(F),getGui:()=>y.current},N=o.createDynamicBean("detailCellRendererCtrl",!0);if(N&&(n.createBean(N),N.init(V,e),R.current=N,u.get("detailRowAutoHeight"))){const F=()=>{if(y.current==null)return;const b=y.current.clientHeight;b!=null&&b>0&&setTimeout(()=>{e.node.setRowHeight(b),(ut(u)||dt(u))&&i.onRowHeightChanged()},0)};m.current=Ue(r,D,F),F()}},[]),W=t.useCallback(D=>{R.current?.registerDetailWithMaster(D)},[]);return a.createElement("div",{className:A,ref:g},E&&a.createElement(Ye,{className:G,...E,modules:f,rowData:h,passGridApi:W}))}),Ts=class extends rt{constructor(e){super("react"),this.processQueuedUpdates=e,this.queueUpdates=!1,this.renderingEngine="react",this.frameworkComponents={agGroupCellRenderer:Oe,agGroupRowRenderer:Oe,agDetailCellRenderer:Ws},this.wrapIncoming=(s,r)=>r==="ensureVisible"?At(s):s()}frameworkComponent(e){return this.frameworkComponents[e]}isFrameworkComponent(e){if(!e)return!1;const s=e.prototype;return!(s&&"getGui"in s)}getLockOnRefresh(){this.queueUpdates=!0}releaseLockOnRefresh(){this.queueUpdates=!1,this.processQueuedUpdates()}shouldQueueUpdates(){return this.queueUpdates}runWhenReadyAsync(){return Gt()}},Hs=class extends t.Component{constructor(){super(...arguments),this.apiListeners=[],this.setGridApi=e=>{this.api=e,this.apiListeners.forEach(s=>s(e))}}registerApiListener(e){this.apiListeners.push(e)}componentWillUnmount(){this.apiListeners.length=0}render(){return a.createElement(Ye,{...this.props,passGridApi:this.setGridApi})}};export{Hs as A};
