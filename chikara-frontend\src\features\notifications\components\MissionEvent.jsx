import { Flame } from "lucide-react";
import { formatCurrency } from "@/utils/currencyHelpers";

function MissionEvent({ details, read }) {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-stroke-0 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                You completed the mission <span className="text-indigo-500">{details.missionName}</span> and earned{" "}
                {details.cash > 0 && <span className="text-green-500">{formatCurrency(details.cash)}</span>}
                {details.exp > 0 && <span className="text-blue-500">{details.exp.toFixed(0)} EXP</span>}
                {details?.itemReward && (
                    <span className="text-pink-500">
                        {details?.itemRewardQuantity}x {details.itemReward}
                    </span>
                )}
            </td>
        </>
    );
}

export default MissionEvent;
