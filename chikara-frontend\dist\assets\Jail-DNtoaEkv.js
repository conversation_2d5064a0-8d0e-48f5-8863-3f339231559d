import{b as l,c as m,t as u,j as e}from"./index-DjvF_jFD.js";import{U as d}from"./UsersTable-BWw7MHs8.js";function x(){const{isLoading:s,error:r,data:a}=l(m.jail.jailList.queryOptions({select:i=>i.sort((o,n)=>o.jailedUntil>n.jailedUntil?1:-1)})),{data:t}=u();return r?"An error has occurred: "+r.message:e.jsx("div",{className:"mb-8 md:mx-auto md:mb-0 md:max-w-6xl",children:e.jsx(d,{data:a,isLoading:s,type:"jail",currentUser:t})})}export{x as default};
