import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

interface UseJobApplyOptions {
    onSuccess?: () => void;
    onError?: (error: Error) => void;
}

const useJobApply = (options?: UseJobApplyOptions) => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    return useMutation(
        api.jobs.apply.mutationOptions({
            onSuccess: (response, variables) => {
                if (!response.success) return;

                // Invalidate job-related queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.jobs.info.key(),
                });
                toast.success("Job application was successful!");
                navigate("/job");

                // Call custom onSuccess callback if provided
                if (options?.onSuccess) {
                    options.onSuccess();
                }
            },
            onError: (error) => {
                console.error("Job application error:", error);

                // Call custom onError callback if provided
                if (options?.onError) {
                    options.onError(error);
                }
            },
        })
    );
};

export default useJobApply;
