import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

interface UseJobChangePayoutTimeOptions {
    onSuccess?: () => void;
    onError?: (error: Error) => void;
}

const useJobChangePayoutTime = (options?: UseJobChangePayoutTimeOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        ...api.jobs.changePayoutTime.mutationOptions(),
        onSuccess: (response) => {
            if (!response.success) return;

            // Invalidate job info to refresh payout time data
            queryClient.invalidateQueries({
                queryKey: api.jobs.info.key(),
            });

            // Call custom onSuccess callback if provided
            if (options?.onSuccess) {
                options.onSuccess();
            }
        },
        onError: (error) => {
            console.error("Job payout time change error:", error);

            // Call custom onError callback if provided
            if (options?.onError) {
                options.onError(error);
            }
        },
    });
};

export default useJobChangePayoutTime;
