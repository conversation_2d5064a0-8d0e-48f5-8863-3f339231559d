import { api, QueryOptions } from "@/helpers/api";
import { client } from "@/lib/orpc";

import { useQuery } from "@tanstack/react-query";

// TODO - Remove this conversion once frontend files are converted to TS
const convertToNumber = (userId: number | string) => {
    return typeof userId === "string" ? Number.parseInt(userId) : userId;
};

export const getUserInfo = async (userId: number | string) => {
    const id = convertToNumber(userId);
    return await client.user.getUserInfo({ id });
};

/**
 * Custom hook to fetch public user profile info by user ID
 */
export const useGetUserInfo = (userId: number | string, options: QueryOptions = {}) => {
    const id = convertToNumber(userId);
    return useQuery(
        api.user.getUserInfo.queryOptions({
            input: { id },
            staleTime: 60 * 1000, // 1 minute
            enabled: !!id,
            ...options,
        })
    );
};

export default useGetUserInfo;
