import { api, QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

/**
 * Custom hook to fetch daily chest items
 */
const useGetDailyChestItems = (options: QueryOptions = {}) => {
    return useQuery(
        api.specialItems.getDailyChestItems.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    );
};

export default useGetDailyChestItems;
