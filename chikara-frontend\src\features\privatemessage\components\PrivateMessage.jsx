import { DisplayAvatar } from "@/components/DisplayAvatar";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { UTCDateMini } from "@date-fns/utc";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import RenderChatText from "../../chat/components/RenderChatText";
import { formatTimeToNow } from "@/helpers/dateHelpers";

export default function PrivateMessage({ message, type = "sentMessage", convoUserInfo, currentUser }) {
    const queryClient = useQueryClient();
    const isSentMessage = type === "sentMessage";
    const navigate = useNavigate();

    // Properly create a mutation for marking a message as read
    const { mutate: markMessageRead } = useMutation(
        api.messaging.markMessageRead.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.messaging.getChatHistory.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.messaging.getUnreadCount.key(),
                });
            },
            onError: (error) => {
                console.error("Failed to mark message as read:", error);
            },
        })
    );

    useEffect(() => {
        if (message?.read === false) {
            markMessageRead({ messageId: message.id });
        }
        // Only re-run when the read state changes
    }, [message?.read, markMessageRead]);

    if (!message) return null;
    const isAnnouncement = message.isGlobal && convoUserInfo.userType === "admin";
    return (
        <li
            key={message.id}
            className={cn(
                "relative flex min-w-40 gap-5 py-2 md:px-3",
                isSentMessage ? "flex-row-reverse self-end" : "flex-row self-start ",
                message?.message?.length < 6 && "text-center"
            )}
        >
            <DisplayAvatar
                className="my-auto h-12 w-auto cursor-pointer rounded-full"
                src={isSentMessage ? currentUser : convoUserInfo}
                onClick={() => navigate(`/profile/${isSentMessage ? currentUser?.id : convoUserInfo?.id}`)}
            />
            <div
                className={cn(
                    "group relative flex size-full min-h-14 flex-col justify-start rounded-lg border px-3 pt-2 pb-5 shadow-xl",
                    isSentMessage ? "border-indigo-500 bg-indigo-800" : "border-gray-500 bg-gray-700",
                    isAnnouncement && "bg-blue-800! border-blue-500!"
                )}
            >
                {message.isGlobal && (
                    <div
                        className={cn(
                            convoUserInfo.userType === "admin"
                                ? "-top-7 text-red-500 text-sm"
                                : "-top-5 text-blue-500 text-xs",
                            "-translate-x-1/2 absolute right-[40%]"
                        )}
                    >
                        {convoUserInfo.userType === "admin" ? "Announcement" : "Megaphone"}
                    </div>
                )}

                <div className={cn(isSentMessage ? "message_arrow_right" : "message_arrow_left")}></div>

                <div className={cn(isAnnouncement ? "text-red-200" : "text-gray-50", "whitespace-pre-wrap text-sm")}>
                    <RenderChatText msg={message} />
                </div>
                <div
                    className={cn(
                        "-bottom-[1.1rem] absolute",
                        isSentMessage ? "right-1 text-right" : "left-1 text-left"
                    )}
                >
                    <p
                        data-tooltip-id="date-tooltip"
                        data-tooltip-content={format(new UTCDateMini(message.createdAt), "PP, p")}
                        className="cursor-pointer whitespace-nowrap font-body font-semibold text-slate-300 text-stroke-0 text-xs sm:mt-0"
                    >
                        <time className="hidden group-hover:block" dateTime={message.createdAt}>
                            {format(new UTCDateMini(message.createdAt), "PP, p")}
                        </time>
                        <time className="group-hover:hidden" dateTime={message.createdAt}>
                            {formatTimeToNow(message.createdAt)} ago
                        </time>
                    </p>
                </div>
            </div>
        </li>
    );
}
