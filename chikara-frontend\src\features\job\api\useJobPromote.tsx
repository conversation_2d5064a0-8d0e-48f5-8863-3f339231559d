import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";

const useJobPromote = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.jobs.promote.mutationOptions({
            onSuccess: (response) => {
                if (!response) {
                    toast.error("You do not meet the requirements for a promotion!");
                    return;
                }

                // Invalidate job-related queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.jobs.info.key(),
                });
                toast.success("You were successfully promoted!");
            },
            onError: (error) => {
                console.error("Job promotion error:", error);
            },
        })
    );
};

export default useJobPromote;
