import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useStartQuest() {
    const queryClient = useQueryClient();

    return useMutation(
        api.quests.start.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.quests.getAvailable.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.quests.getActive.key(),
                });
            },
            onError: (error) => {
                console.error("Failed to start quest:", error);
            },
        })
    );
}
