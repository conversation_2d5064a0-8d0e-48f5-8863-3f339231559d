import { Rival } from "@/features/social/types/social";
import { api } from "@/helpers/api";
import { UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface UpdateRivalNoteParams {
    rivalId: number;
    note: string | null;
}

const useUpdateRivalNote = (options: Partial<UseMutationOptions<Rival, Error, UpdateRivalNoteParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.updateRivalNote.mutationOptions({
            onSuccess: () => {
                toast.success("Rival note updated!");
                // Invalidate rivals list to refresh the data
                queryClient.invalidateQueries({ queryKey: api.social.getRivals.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to update rival note");
            },
            ...options,
        })
    );
};

export default useUpdateRivalNote;
