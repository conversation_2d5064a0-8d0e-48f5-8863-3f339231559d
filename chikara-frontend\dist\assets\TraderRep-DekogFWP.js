import{b as d,c as g,y as m,j as t,h as a}from"./index-DjvF_jFD.js";function x({shopId:s,heartWidth:l}){const{data:n}=d(g.shops.getTraderRep.queryOptions({input:{shopId:Number(s)},enabled:!!s&&!isNaN(Number(s))&&Number(s)>0})),{MAX_TRADER_REP:p}=m(),c=(r,e)=>{if(!r)return"polygon(0 0, 100% 0, 100% 100%, 0 100%)";const o=r?.reputationLevel;if(!o)return"polygon(0 0, 0% 0, 0% 100%, 0 100%)";const i=Math.max(0,(o-e+1)*100);return`polygon(0 0, ${i}% 0, ${i}% 100%, 0 100%)`},u=(r,e)=>{if(!r)return"opacity-50";const o=r?.reputationLevel;return!o||o<1?e===0?"opacity-100":"opacity-50":o>=e?"opacity-100":"opacity-50"};return t.jsx(t.Fragment,{children:[...new Array(p||4)].map((r,e)=>t.jsxs("div",{className:a(u(n,e),"relative"),children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a(l,"stroke-2 stroke-gray-800 text-gray-600/50 drop-shadow-md"),viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})},e),t.jsx("svg",{style:{clipPath:c(n,e+1)},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:a(l,"absolute top-0 left-0 stroke-2 stroke-gray-800 text-pink-600"),children:t.jsx("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})})]},e))})}export{x as T};
