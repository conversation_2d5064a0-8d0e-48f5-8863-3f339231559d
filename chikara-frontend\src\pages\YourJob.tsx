import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useIsTalentUnlocked from "@/hooks/api/useIsTalentUnlocked";
import useGameConfig from "@/hooks/useGameConfig";
import { Banknote, Star, Clock, TrendingUp, Briefcase } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import JobPayoutTimeModal from "../features/job/components/JobPayoutTimeModal";
import RankCard from "../features/job/components/RankCard";
import useJobInfo from "../features/job/api/useJobInfo";
import useJobRequirements from "../features/job/api/useJobRequirements";
import useJobPromote from "../features/job/api/useJobPromote";
import { getJobImage } from "../features/job/helpers/getJobImage";
import { UserStat } from "@/types/user";
import { formatCurrency } from "@/utils/currencyHelpers";

export default function YourJob() {
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();

    const jobsConfig = useGameConfig();

    const { data: currentJob, isLoading } = useJobInfo();
    const { data: currentUser } = useFetchCurrentUser();

    const { mutate: applyPromotion } = useJobPromote();
    const investorTalent = useIsTalentUnlocked("investor");

    useEffect(() => {
        if (!isLoading && !currentJob) {
            navigate("/joblistings");
        }
    }, [isLoading, currentJob, navigate]);

    // Fetch requirements for next rank (current level + 1)
    const { data: nextRankRequirements } = useJobRequirements({
        level: currentJob?.level ? currentJob.level + 1 : 1,
        jobId: currentJob?.id || 1,
        options: {
            enabled: Boolean(currentJob?.id && currentJob?.level),
        },
    });

    // Fetch requirements for future rank (current level + 2)
    const { data: futureRankRequirements } = useJobRequirements({
        level: currentJob?.level ? currentJob.level + 2 : 1,
        jobId: currentJob?.id || 1,
        options: {
            enabled: Boolean(currentJob?.id && currentJob?.level),
        },
    });

    // Calculate rank data with salary
    const nextRank =
        nextRankRequirements && currentJob
            ? {
                  ...nextRankRequirements,
                  salary: currentJob.salary + 100,
                  level: currentJob.level ? currentJob.level + 1 : 1,
                  payment: currentJob.salary + 100,
              }
            : { level: 1, payment: 0 };

    const futureRank =
        futureRankRequirements && currentJob
            ? {
                  ...futureRankRequirements,
                  salary: currentJob.salary + 200,
                  level: currentJob.level ? currentJob.level + 2 : 2,
                  payment: currentJob.salary + 200,
              }
            : { level: 2, payment: 0 };

    const getModifiedSalary = (salary: number) => {
        return investorTalent ? Math.round(salary * (investorTalent.modifier || 1)) : salary;
    };

    useEffect(() => {
        if (currentUser?.jobId && !currentUser?.jobPayoutHour) {
            setOpen(true);
        }
    }, [currentUser?.jobId, currentUser?.jobPayoutHour]);

    if (jobsConfig?.JOBS_DISABLED) {
        return (
            <div className="mt-10 flex flex-col dark:text-slate-200">
                <div className="mx-auto text-center">
                    <h2 className="text-xl">Jobs currently Disabled</h2>
                    <p>Please return later.</p>
                </div>
            </div>
        );
    }

    const checkIfMeetsStatReqs = (statName: UserStat, statReqValue: number): boolean => {
        if (!currentUser) return false;
        return currentUser[statName] >= statReqValue;
    };

    const getStatRequirements = () => {
        const statReq: { type: UserStat; name: string }[] = [];

        switch (currentJob?.name) {
            case "Nemar Ramen":
                statReq.push({ type: "strength", name: "STR" });
                statReq.push({ type: "defence", name: "DEF" });
                break;
            case "Robo-San Construction":
                statReq.push({ type: "dexterity", name: "DEX" });
                statReq.push({ type: "endurance", name: "END" });
                break;
            case "Costar Cafe":
                statReq.push({ type: "strength", name: "STR" });
                statReq.push({ type: "dexterity", name: "DEX" });
                break;
            case "PGS Corp":
                statReq.push({ type: "strength", name: "STR" });
                statReq.push({ type: "dexterity", name: "DEX" });
                statReq.push({ type: "defence", name: "DEF" });
                break;
            case "5-11 Convenience Store":
                statReq.push({ type: "intelligence", name: "INT" });
                statReq.push({ type: "endurance", name: "END" });
                break;
            case "Envydia Computers":
                statReq.push({ type: "intelligence", name: "INT" });
                break;
        }

        return statReq;
    };

    if (isLoading)
        return (
            <div className="flex h-screen items-center justify-center">
                <div className="text-lg text-gray-400">Loading job information...</div>
            </div>
        );

    if (!currentJob) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-slate-900 to-black p-4 md:p-8">
            <JobPayoutTimeModal currentUser={currentUser} open={open} setOpen={setOpen} />

            {/* Main Container */}
            <div className="mx-auto max-w-7xl">
                {/* Header Section */}
                <div className="mb-8 overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800 via-slate-850 to-slate-900 shadow-2xl border border-slate-700/50">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-5">
                        <div
                            className="h-full w-full"
                            style={{
                                backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%)`,
                            }}
                        />
                    </div>

                    <div className="relative z-10">
                        {/* Main Content */}
                        <div className="flex flex-col lg:flex-row">
                            {/* Job Info Section */}
                            <div className="flex-1 p-5 lg:p-6">
                                <div className="flex flex-col sm:flex-row sm:items-center gap-4 lg:gap-5">
                                    {/* Job Image */}
                                    <div className="flex-shrink-0">
                                        <div className="relative">
                                            <img
                                                className="size-16 sm:size-18 lg:size-20 rounded-xl shadow-2xl ring-2 ring-slate-600/30"
                                                src={getJobImage(currentJob.name)}
                                                alt={currentJob.name}
                                            />
                                            <div className="absolute -bottom-1 -right-1 bg-slate-700 rounded-full p-1 shadow-lg">
                                                <Briefcase className="size-3 text-slate-300" />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Job Details */}
                                    <div className="flex-1 space-y-3">
                                        <div>
                                            <h1 className="text-2xl lg:text-3xl font-bold text-white mb-1 tracking-tight">
                                                {currentJob.name}
                                            </h1>
                                            <p className="text-slate-400 text-xs lg:text-sm">Your current workplace</p>
                                        </div>

                                        {/* Stats Row */}
                                        <div className="flex flex-wrap gap-3">
                                            <div className="flex items-center gap-2 bg-slate-700/40 rounded-lg px-3 py-2 backdrop-blur-sm">
                                                <div className="bg-blue-500/20 rounded-md p-1.5">
                                                    <Star className="size-3 text-blue-400" />
                                                </div>
                                                <div>
                                                    <p className="text-[10px] text-slate-400 uppercase tracking-wider font-medium">
                                                        Rank
                                                    </p>
                                                    <p className="text-white font-semibold text-sm">
                                                        {currentJob.level}
                                                    </p>
                                                </div>
                                            </div>

                                            <div className="flex items-center gap-2 bg-slate-700/40 rounded-lg px-3 py-2 backdrop-blur-sm">
                                                <div className="bg-green-500/20 rounded-md p-1.5">
                                                    <Banknote className="size-3 text-green-400" />
                                                </div>
                                                <div>
                                                    <p className="text-[10px] text-slate-400 uppercase tracking-wider font-medium">
                                                        Daily Salary
                                                    </p>
                                                    <p className="text-white font-semibold text-sm">
                                                        {formatCurrency(getModifiedSalary(currentJob.salary))}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons Section */}
                            <div className="lg:w-64 border-t lg:border-t-0 lg:border-l border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
                                <div className="p-4 lg:p-5 h-full flex flex-col justify-center">
                                    <h3 className="text-sm font-semibold text-white mb-3">Quick Actions</h3>

                                    <div className="space-y-2">
                                        <button
                                            className="w-full flex items-center justify-between gap-2 rounded-lg bg-slate-700/60 hover:bg-slate-700 px-3 py-2.5 font-medium text-white transition-all duration-200 hover:shadow-lg group"
                                            onClick={() => setOpen(true)}
                                        >
                                            <div className="flex items-center gap-2">
                                                <Clock className="size-3.5 text-slate-400 group-hover:text-slate-300" />
                                                <span className="text-xs">Payout Time</span>
                                            </div>
                                            <div className="size-1.5 rounded-full bg-slate-500 group-hover:bg-slate-400" />
                                        </button>

                                        <button
                                            className="w-full flex items-center justify-between gap-2 rounded-lg bg-slate-700/60 hover:bg-slate-700 px-3 py-2.5 font-medium text-white transition-all duration-200 hover:shadow-lg group"
                                            onClick={() => navigate("/joblistings")}
                                        >
                                            <div className="flex items-center gap-2">
                                                <Briefcase className="size-3.5 text-slate-400 group-hover:text-slate-300" />
                                                <span className="text-xs">Find New Job</span>
                                            </div>
                                            <div className="size-1.5 rounded-full bg-slate-500 group-hover:bg-slate-400" />
                                        </button>

                                        <button
                                            className="w-full flex items-center justify-between gap-2 rounded-lg bg-gradient-to-r from-blue-600/80 to-indigo-600/80 hover:from-blue-600 hover:to-indigo-600 px-3 py-2.5 font-medium text-white shadow-lg transition-all duration-200 hover:shadow-xl group"
                                            onClick={() => applyPromotion({})}
                                        >
                                            <div className="flex items-center gap-2">
                                                <TrendingUp className="size-3.5 text-blue-200 group-hover:text-white" />
                                                <span className="text-xs">Request Promotion</span>
                                            </div>
                                            <div className="size-1.5 rounded-full bg-blue-300 group-hover:bg-white" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Career Progression Section */}
                <div className="space-y-6">
                    <h2 className="text-center text-2xl font-bold text-white">Career Progression</h2>

                    {/* Progression Cards */}
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Current Rank */}
                        <RankCard
                            rank={{}}
                            title="Current"
                            level={currentJob.level || 0}
                            salary={currentJob.salary || 0}
                            accent="current"
                            getModifiedSalary={getModifiedSalary}
                            getStatRequirements={getStatRequirements}
                            checkIfMeetsStatReqs={checkIfMeetsStatReqs}
                        />

                        {/* Next Rank */}
                        <RankCard
                            rank={nextRank}
                            title="Next"
                            level={nextRank.level}
                            salary={nextRank.payment}
                            accent="next"
                            getModifiedSalary={getModifiedSalary}
                            getStatRequirements={getStatRequirements}
                            checkIfMeetsStatReqs={checkIfMeetsStatReqs}
                        />

                        {/* Future Rank */}
                        <RankCard
                            rank={futureRank}
                            title="Future"
                            level={futureRank.level}
                            salary={futureRank.payment}
                            accent="future"
                            getModifiedSalary={getModifiedSalary}
                            getStatRequirements={getStatRequirements}
                            checkIfMeetsStatReqs={checkIfMeetsStatReqs}
                        />
                    </div>

                    {/* Visual Progress Indicator */}
                    <div className="relative mt-8 hidden md:block">
                        <div className="absolute inset-0 flex items-center">
                            <div className="h-1 w-full bg-gradient-to-r from-slate-600 via-indigo-600 to-purple-600 rounded-full" />
                        </div>
                        <div className="relative flex justify-between">
                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-slate-700 ring-4 ring-slate-900">
                                <div className="h-6 w-6 rounded-full bg-slate-400" />
                            </div>
                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-indigo-700 ring-4 ring-slate-900">
                                <div className="h-6 w-6 rounded-full bg-indigo-400" />
                            </div>
                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-700 ring-4 ring-slate-900">
                                <div className="h-6 w-6 rounded-full bg-purple-400" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
