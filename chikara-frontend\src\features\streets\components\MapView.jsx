import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { BowArrow, CloudMoon, CloudSun, Dumbbell, Shield, Sunrise, Sunset } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useSessionStore } from "../../../app/store/stores";
import { getAllFutureNodes } from "../helpers/MapHelpers";
import ReactFlow from "./ReactFlow";
import { useActivateNode, useAdvance } from "../api/useRoguelikeMutations";

const getTimeOfDay = (type) => {
    const date = new Date();
    const hour = date.getHours();

    if (hour >= 6 && hour < 12) {
        if (type === "icon") {
            return <Sunrise className="ml-1 inline" />;
        }
        return "Morning";
    }
    if (hour >= 12 && hour < 18) {
        if (type === "icon") {
            return <Sunset className="ml-1 inline" />;
        }
        return "Afternoon";
    }
    if (hour >= 18 && hour <= 21) {
        if (type === "icon") {
            return <CloudSun className="ml-1 inline" />;
        }
        return "Evening";
    }
    if (hour >= 21 && hour <= 6) {
        if (type === "icon") {
            return <CloudMoon className="ml-1 inline" />;
        }
        return "Night";
    }
};

export default function MapView({ currentMap }) {
    const { setInEncounter } = useSessionStore();
    const [buttonDisabled, setButtonDisabled] = useState(false);

    const isMobile = useCheckMobileScreen();
    const activateNodeMutation = useActivateNode();
    const advanceMutation = useAdvance();

    const [mapData, setMapData] = useState({
        nodes: [],
        edges: [],
        futureNodes: [],
        level: 0,
        playerLocation: 0,
    });

    const strBuff = currentMap?.strBuff - 1;
    const defBuff = currentMap?.defBuff - 1;
    const dexBuff = currentMap?.dexBuff - 1;

    const { data: currentUser } = useFetchCurrentUser();

    const activateNode = (encounterType) => {
        if (buttonDisabled) return;
        setButtonDisabled(true);

        activateNodeMutation.mutate(undefined, {
            onSuccess: (data) => {
                if (encounterType === "character") {
                    setInEncounter(true);
                }
                if (encounterType === "buff" && data?.info) {
                    if (data.info.strBuff) {
                        toast.success(`Received a +${(data.info.strBuff * 100).toFixed(0)}% Strength boost!`);
                    }
                    if (data.info.defBuff) {
                        toast.success(`Received a +${(data.info.defBuff * 100).toFixed(0)}% Defence boost!`);
                    }
                    if (data.info.dexBuff) {
                        toast.success(`Received a +${(data.info.dexBuff * 100).toFixed(0)}% Dexterity boost!`);
                    }
                }
                setButtonDisabled(false);
            },
            onError: () => {
                setButtonDisabled(false);
            },
        });
    };

    const advanceNode = (nodeId) => {
        if (buttonDisabled) return;
        setButtonDisabled(true);

        const id = Number.parseInt(nodeId);
        advanceMutation.mutate(
            { node: id },
            {
                onSuccess: () => {
                    const currentNode = currentMap.nodes[nodeId];
                    activateNode(currentNode.encounterType);
                },
                onError: () => {
                    setButtonDisabled(false);
                },
            }
        );
    };

    useEffect(() => {
        if (Object.keys(currentMap).length > 0 && !currentMap?.currentNodeComplete) {
            const currentNode = currentMap.nodes[currentMap?.playerLocation];
            activateNode(currentNode.encounterType);
        }
    }, []);

    useEffect(() => {
        if (Object.keys(currentMap).length === 0) return;
        let nodes = currentMap?.nodes;
        if (!nodes) {
            nodes = [];
        }

        const mapEdges = [];
        nodes?.forEach((node, index) => {
            if (currentMap.playerLocation === index) {
                node.playerHere = true;
                node?.edges?.forEach((edge) => {
                    nodes[edge].isNextNode = true;
                });
            }
            node.id = index;
            if (node.edges) {
                node?.edges?.forEach((edge) => {
                    mapEdges.push({
                        source: index,
                        target: edge,
                    });
                });
            }
        });

        const futureNodes = getAllFutureNodes([...nodes]);

        setMapData({
            nodes: nodes,
            edges: mapEdges,
            futureNodes: futureNodes,
            playerLocation: currentMap?.playerLocation,
        });
    }, [currentMap?.playerLocation, currentUser?.roguelikeLevel]);

    const onClickNode = (nodeId) => {
        if (buttonDisabled) return;
        advanceNode(nodeId);
    };

    return (
        <div className="relative h-full border-slate-600 border-t md:h-[80dvh] md:rounded-lg md:border-2 md:shadow-lg">
            <img
                className="absolute top-0 left-0 z-0 size-full object-cover md:rounded-lg dark:brightness-90"
                src="https://d13cmcqz8qkryo.cloudfront.net/static/misc/roguelikeMap3.webp"
                alt="Map background"
                onError={(e) => {
                    e.target.src = null;
                }}
            />

            <div className="pointer-events-none absolute top-2 left-2 z-10 rounded-lg bg-gray-900/50 px-2 text-custom-yellow">
                <p className="text-2xl lg:text-4xl">Zone {currentUser?.roguelikeLevel}</p>
            </div>
            <div
                className={cn(
                    isMobile ? "right-2 bottom-3" : "top-16 left-2",
                    "pointer-events-none absolute z-10 flex flex-col rounded-lg bg-gray-900/50 px-2 py-1 text-center text-white lg:text-xl"
                )}
            >
                <p className="text-blue-500 text-xl">{capitaliseFirstLetter(currentMap?.location)}</p>
                <p className="my-auto">
                    {getTimeOfDay()} {getTimeOfDay("icon")}
                </p>
                {strBuff > 0 || defBuff > 0 || dexBuff > 0 ? (
                    <>
                        <hr className="my-1 border-gray-950/25" />
                        <p className="text-left text-gray-300 text-xs uppercase">Buffs</p>
                        <div className="mr-4 flex flex-col">
                            <div className="grid grid-cols-2">
                                <span className="text-green-600">+{Math.round(strBuff * 100)}%</span>
                                <span>
                                    STR <Dumbbell className="my-auto inline" />
                                </span>
                            </div>
                            <div className="grid grid-cols-2">
                                <span className="text-green-600">+{Math.round(defBuff * 100)}%</span>
                                <span>
                                    DEF <Shield className="my-auto inline" />
                                </span>
                            </div>
                            <div className="grid grid-cols-2">
                                <span className="text-green-600">+{Math.round(dexBuff * 100)}%</span>
                                <span>
                                    DEX <BowArrow className="my-auto inline" />
                                </span>
                            </div>
                        </div>
                    </>
                ) : null}
            </div>

            <div className="relative h-[calc(100dvh-5rem-3.75rem)] bg-black/20 md:h-[80dvh] md:rounded-lg">
                <ReactFlow data={mapData} advanceNode={advanceNode} onClickNode={onClickNode} />
            </div>
        </div>
    );
}
