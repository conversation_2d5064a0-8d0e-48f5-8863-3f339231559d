import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

function useIsQuestInProgress(questName: string): boolean {
    const { data: questProgress } = useQuery(
        api.quests.getProgress.queryOptions({
            input: { activeOnly: false },
            staleTime: Number.POSITIVE_INFINITY,
        })
    );

    const questInProgress = useMemo(() => {
        const questsInProgress = questProgress?.filter((quest) => quest.questStatus === "in_progress");
        if (!questsInProgress) return false;

        const isQuestInProgress = questsInProgress?.find((quest) => quest.quest.name === questName);

        if (isQuestInProgress) return true;
        return false;
    }, [questProgress, questName]);

    if (!questProgress) return false;

    return questInProgress;
}

export default useIsQuestInProgress;
