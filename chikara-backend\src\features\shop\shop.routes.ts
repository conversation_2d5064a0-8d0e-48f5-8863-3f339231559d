import { isLoggedInAuth, canMakeStateChang<PERSON>Auth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as ShopController from "./shop.controller.js";
import * as ShopAdminController from "./shop.admin.js";
import shopSchema from "./shop.validation.js";
import { z } from "zod";

// Additional schemas for query parameters
const shopInfoSchema = z.object({
    shopId: z.number().int().positive(),
});

const traderRepSchema = z.object({
    shopId: z.number().int().positive(),
});

export const shopRouter = {
    // Get all shops
    shopList: isLoggedInAuth.handler(async () => {
        const response = await ShopController.shopList();
        return handleResponse(response);
    }),

    // Get detailed information about a specific shop
    shopInfo: isLoggedInAuth.input(shopInfoSchema).handler(async ({ input }) => {
        const response = await ShopController.shopInfo(input.shopId);
        return handleResponse(response);
    }),

    // Get trader reputation for a specific shop
    getTraderRep: isLoggedInAuth.input(traderRepSchema).handler(async ({ input, context }) => {
        const response = await ShopController.getTraderRep(context.user.id, input.shopId);
        return handleResponse(response);
    }),

    // Purchase an item from a shop
    purchaseItem: canMakeStateChangesAuth.input(shopSchema.purchaseItemSchema).handler(async ({ input, context }) => {
        const response = await ShopController.purchaseItem(input.id, input.amount, context.user.id);
        return handleResponse(response);
    }),

    // Sell an item to a shop
    sellItem: canMakeStateChangesAuth.input(shopSchema.sellItemSchema).handler(async ({ input, context }) => {
        const response = await ShopController.sellItem(input.userItemId, input.amount, context.user.id);
        return handleResponse(response);
    }),
};

// ===============================================
// Admin Routes
// ===============================================

export const shopAdminRouter = {
    // Create a new shop
    createShop: adminAuth.input(shopSchema.createShopSchema).handler(async ({ input }) => {
        const response = await ShopAdminController.createShop(input);
        return handleResponse(response);
    }),

    // Update an existing shop
    updateShop: adminAuth.input(shopSchema.editShopSchema).handler(async ({ input }) => {
        const { id, ...updateData } = input;
        const response = await ShopAdminController.editShop(id, updateData);
        return handleResponse(response);
    }),

    // Delete a shop
    deleteShop: adminAuth.input(shopSchema.deleteShopSchema).handler(async ({ input }) => {
        const response = await ShopAdminController.deleteShop(input.id);
        return handleResponse(response);
    }),

    // Create a new shop listing
    createShopListing: adminAuth.input(shopSchema.createShopListingSchema).handler(async ({ input }) => {
        const response = await ShopAdminController.createShopListing(input);
        return handleResponse(response);
    }),

    // Edit an existing shop listing
    editShopListing: adminAuth.input(shopSchema.editShopListingSchema).handler(async ({ input }) => {
        const { id, ...updateData } = input;
        const response = await ShopAdminController.editShopListing(id, updateData);
        return handleResponse(response);
    }),

    // Delete a shop listing
    deleteShopListing: adminAuth.input(shopSchema.deleteShopListingSchema).handler(async ({ input }) => {
        const response = await ShopAdminController.deleteShopListing(input.id);
        return handleResponse(response);
    }),
};
