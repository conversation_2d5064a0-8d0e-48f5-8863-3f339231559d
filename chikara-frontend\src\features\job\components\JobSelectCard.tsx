import { getJobImage } from "../helpers/getJobImage";
import useIsTalentUnlocked from "@/hooks/api/useIsTalentUnlocked";
import { cn } from "@/lib/utils";
import { CheckCircle, XCircle, Banknote, MapPin, LogIn } from "lucide-react";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import useJobApply from "../api/useJobApply";
import { User } from "@/types/user";
import { formatCurrency } from "@/utils/currencyHelpers";

interface Job {
    id: number;
    name: string;
    avatar?: string;
    baseRequirements: {
        payment: number;
        [key: string]: number;
    };
    level?: number;
    salary?: number;
}

export default function JobSelectCard({
    job,
    currentJob,
    currentUser,
}: {
    job: Job;
    currentJob: number;
    currentUser: User;
}) {
    const investorTalent = useIsTalentUnlocked("investor");

    const getModifiedSalary = (salary: number) => {
        return investorTalent ? Math.round(salary * (investorTalent.modifier || 1)) : salary;
    };
    const navigate = useNavigate();

    const backgroundImageStyle: React.CSSProperties = {
        backgroundImage: job?.avatar ? `url(${job.avatar})` : "none",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        backgroundSize: "cover",
        backgroundColor: "black",
        opacity: "1",
        width: "100%",
        height: "100%",
        position: "absolute",
        top: "0",
        left: "0",
        borderRadius: "0.75rem",
    };

    const jobApply = useJobApply({
        onSuccess: () => {
            toast.success("You were hired!");
            navigate("/job");
        },
        onError: () => {
            toast.error("Your stats don't meet the requirements for this job!");
        },
    });

    const jobApplication = (jobId: number) => {
        if (jobId === currentJob) {
            toast.error("You already work for this company!");
        } else {
            jobApply.mutate({ jobId });
        }
    };

    const filteredItems = Object.entries(job.baseRequirements)
        .filter(([key, value]) => key !== "payment" && (value as number) > 0)
        .sort((a, b) => (b[1] as number) - (a[1] as number));

    const statsArray = filteredItems.map(([key, value]) => ({
        [key]: value,
    }));

    const shortenText = (stat: string) => {
        const statMap = {
            strength: "STR",
            intelligence: "INT",
            dexterity: "DEX",
            defence: "DEF",
            endurance: "END",
            vitality: "VIT",
        };
        return statMap[stat as keyof typeof statMap] || "";
    };

    const checkIfMeetsStatReqs = (stat: any, bool = false) => {
        const statName = Object.keys(stat)[0];
        const statReqValue = Object.values(stat)[0] as number;
        if (currentUser[statName] >= statReqValue) {
            return bool ? true : "meets";
        } else {
            return bool ? false : "fails";
        }
    };

    const isCurrentJob = job.id === currentJob;

    return (
        <div
            className={cn(
                "group relative overflow-hidden rounded-xl border transition-all duration-300",
                isCurrentJob
                    ? "border-emerald-600 shadow-emerald-900/20"
                    : "border-slate-700 hover:border-slate-600 hover:shadow-xl"
            )}
        >
            {/* Background Image */}
            <div style={backgroundImageStyle} />

            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/40" />

            {/* Subtle Pattern Overlay */}
            <div className="absolute inset-0 opacity-10">
                <div
                    className="h-full w-full"
                    style={{
                        backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.1) 35px, rgba(255,255,255,.1) 70px)`,
                    }}
                />
            </div>

            {/* Current Job Highlight Overlay */}
            {isCurrentJob && <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/30 to-transparent" />}

            {/* Content */}
            <div className="relative z-10 p-6">
                {/* Header */}
                <div className="mb-4 flex items-start justify-between">
                    <div className="rounded-lg bg-black/40 p-2 backdrop-blur-sm">
                        <img
                            className="size-16 rounded-md shadow-lg transition-transform group-hover:scale-105"
                            src={getJobImage(job.name)}
                            alt={job.name}
                        />
                    </div>
                    {isCurrentJob && (
                        <span className="rounded-full bg-emerald-900/80 px-3 py-1 text-xs font-semibold text-emerald-300 backdrop-blur-sm border border-emerald-600/50">
                            CURRENT JOB
                        </span>
                    )}
                </div>

                {/* Job Info */}
                <div className="mb-4">
                    <h3 className="mb-2 text-xl font-bold text-white drop-shadow-lg">{job.name}</h3>
                    <div className="flex items-center gap-2 rounded-lg bg-black/40 px-3 py-2 backdrop-blur-sm">
                        <Banknote className="size-4 text-custom-yellow" />
                        <span className="font-semibold text-custom-yellow">
                            {formatCurrency(getModifiedSalary(job.baseRequirements.payment))}/day
                        </span>
                    </div>
                </div>

                {/* Requirements */}
                <div className="space-y-3">
                    <p className="text-xs font-semibold uppercase tracking-wider text-gray-300 drop-shadow">
                        Requirements
                    </p>
                    <div className={cn("grid gap-2", statsArray.length > 2 ? "grid-cols-3" : "grid-cols-2")}>
                        {statsArray.map((stat) => {
                            const statName = Object.keys(stat)[0];
                            const statValue = Object.values(stat)[0];
                            const meetsReq = checkIfMeetsStatReqs(stat, true);

                            return (
                                <div
                                    key={statName}
                                    className={cn(
                                        "flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors backdrop-blur-sm border",
                                        meetsReq
                                            ? "bg-emerald-900/40 text-emerald-300 border-emerald-600/50"
                                            : "bg-red-900/40 text-red-300 border-red-600/50"
                                    )}
                                >
                                    <span>{shortenText(statName)}</span>
                                    <div className="flex items-center gap-1">
                                        <span>{statValue as number}</span>
                                        {meetsReq ? <CheckCircle className="size-3" /> : <XCircle className="size-3" />}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>

                {/* Apply Button */}
                <button
                    disabled={isCurrentJob}
                    className={cn(
                        "mt-4 flex w-full items-center justify-center gap-2 rounded-lg py-3 font-semibold text-white transition-all backdrop-blur-sm border",
                        isCurrentJob
                            ? "cursor-not-allowed bg-slate-800/50 opacity-50 border-slate-600/50"
                            : "bg-gradient-to-r from-indigo-600/80 to-indigo-700/80 hover:from-indigo-700/90 hover:to-indigo-800/90 hover:shadow-lg border-indigo-500/50 hover:border-indigo-400/50"
                    )}
                    onClick={() => jobApplication(job.id)}
                >
                    {isCurrentJob ? (
                        <>
                            <MapPin className="size-4" />
                            <span>Current Workplace</span>
                        </>
                    ) : (
                        <>
                            <LogIn className="size-4" />
                            <span>Apply Now</span>
                        </>
                    )}
                </button>
            </div>
        </div>
    );
}
