import{b as vt,c as U,bN as Ft,e as St,g as kt,am as de,j as t,S as Gt,h as Ae,bG as Ht,B as Fe,r as n,m as A,t as Ut,y as Yt}from"./index-DjvF_jFD.js";import{C as yt}from"./circle-check-big-sPkhu49B.js";const Vt=({ticketCost:e,disabled:s})=>{const{data:a,isLoading:l}=vt(U.casino.getLottery.queryOptions({staleTime:3e4})),{data:c,isLoading:N}=vt(U.casino.checkLotteryEntry.queryOptions({input:{id:a?.id?.toString()||""},enabled:!!a?.id,staleTime:15e3})),y=Ft(),b=St(),x=kt(U.casino.enterLottery.mutationOptions({onSuccess:()=>{b.invalidateQueries({queryKey:U.casino.getLottery.key()}),b.invalidateQueries({queryKey:U.casino.checkLotteryEntry.key()}),b.invalidateQueries({queryKey:U.user.getCurrentUserInfo.key()}),de.success("Successfully entered lottery!")},onError:k=>{const z=k.message||"Unknown error occurred";console.error(z),de.error(z)}})),h=()=>{a?.id&&x.mutate({lotteryId:a.id})};return l||N?t.jsx(Gt,{center:!0}):t.jsx("div",{className:"mb-4 sm:flex sm:items-center",children:t.jsx("div",{className:Ae("mx-auto mt-3 flex flex-col rounded-lg border-2 border-indigo-600 bg-gray-800 py-2.5 lg:block lg:w-fit lg:px-14",s&&"opacity-50"),children:a?t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"mx-6 flex justify-between lg:justify-center lg:gap-10",children:[t.jsxs("div",{className:"my-auto flex flex-col",children:[t.jsxs("p",{className:"text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200",children:[" ","Current Prize Pool"," "]}),t.jsx("h1",{className:"text-center font-normal text-gray-900 text-stroke-sm text-xl leading-6 lg:text-2xl dark:text-slate-400",children:t.jsxs("span",{className:"text-custom-yellow",children:["¥",a?.prizeAmount]})})]}),t.jsxs("div",{className:"my-auto flex flex-col gap-1",children:[t.jsxs("p",{className:"text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200",children:[" ","Entry Cost"," "]}),t.jsx("div",{className:"text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-xl",children:t.jsxs("p",{className:"text-green-500",children:["¥",e]})})]}),t.jsxs("div",{className:"my-auto flex flex-col",children:[t.jsxs("p",{className:"text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200",children:[" ","Finishes in"," "]}),t.jsx("p",{className:"text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-2xl",children:Ht(y)})]}),c?t.jsxs("div",{className:"m-auto hidden gap-1 text-green-500 text-xl lg:flex",children:[t.jsx(yt,{className:"my-auto"}),"Entered"]}):t.jsx(Fe,{className:"font-medium! text-base! mx-auto! mt-1! lg:block! hidden! text-stroke-sm",type:"primary",disabled:s||x.isPending,onClick:()=>h(),children:x.isPending?"Entering...":"Buy Ticket"})]}),c?t.jsxs("div",{className:"m-auto mt-2 flex gap-1 text-green-500 text-xl lg:hidden",children:[t.jsx(yt,{className:"my-auto"}),"Entered"]}):t.jsx(Fe,{className:"font-medium! text-base! mx-auto! mt-2! lg:hidden! h-9! text-stroke-sm",type:"primary",disabled:s||x.isPending,onClick:()=>h(),children:x.isPending?"Entering...":"Buy Ticket"})]}):t.jsx("p",{children:"No Lottery currently running!"})})})};function Qt(){const e=typeof window<"u";function s(){const c=e?window.innerWidth:null,N=e?window.innerHeight:null;return{width:c,height:N}}const[a,l]=n.useState(s());return n.useEffect(()=>{if(e){let c=function(){l(s())};return window.addEventListener("resize",c),()=>window.removeEventListener("resize",c)}},[e]),a}var Ge=function(){return Ge=Object.assign||function(e){for(var s,a=1,l=arguments.length;a<l;a++)for(var c in s=arguments[a])Object.prototype.hasOwnProperty.call(s,c)&&(e[c]=s[c]);return e},Ge.apply(this,arguments)},nt=typeof window<"u"?A.useLayoutEffect:A.useEffect,Kt="index-module_slot_wrap__ZT-DX",Xt="index-module_slot__DpPgW",Zt="index-module_separator__7GhtE",Jt="index-module_numbers__sqlqm",We="index-module_num__j6XH3",$t="index-module_top_dummy_list__veRmf";(function(e,s){s===void 0&&(s={});var a=s.insertAt;if(typeof document<"u"){var l=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.type="text/css",a==="top"&&l.firstChild?l.insertBefore(c,l.firstChild):l.appendChild(c),c.styleSheet?c.styleSheet.cssText=e:c.appendChild(document.createTextNode(e))}})(`.index-module_slot_wrap__ZT-DX {
  display: inline-block;
  white-space: nowrap;
}

.index-module_slot__DpPgW {
  display: inline-block;
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  will-change: transform;
}

.index-module_separator__7GhtE {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
}

.index-module_numbers__sqlqm {
  display: block;
}

.index-module_num__j6XH3 {
  display: block;
}

.index-module_top_dummy_list__veRmf {
  position: absolute;
  top: 0;
  transform: translateY(-100%);
}

[aria-hidden=true] {
  user-select: none;
}`);var en=[",","."," "],tn=/\u0020/,nn=/\u00A0/,rn=/\u202F/,sn="slot-counter",an="slot-counter-separator",Ct="slot-counter-item",ln="slot-counter-item-numbers",Pe="slot-counter-item-number",on="slot-counter-item-number-value",Y=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];return e.filter(Boolean).join(" ")},bt=function(e,s){for(var a=[],l=e;l<s;l+=1)a.push(l);return a},Nt=function(e,s){for(var a=[],l=e;l!==s;)a.push(l),(l+=1)===10&&(l=0);return a},jt=function(e,s){var a=Math.random()*(s-e);return Math.floor(a+e)},wt=function(e){for(var s,a=function(N,y,b){for(var x,h=0,k=y.length;h<k;h++)!x&&h in y||(x||(x=Array.prototype.slice.call(y,0,h)),x[h]=y[h]);return N.concat(x||Array.prototype.slice.call(y))}([],e),l=a.length-1;l>0;l-=1){var c=Math.floor(Math.random()*(l+1));s=[a[c],a[l]],a[l]=s[0],a[c]=s[1]}return a},_e=function(e){return typeof e=="number"?e:(Array.isArray(e)?e.join(""):e).replace(/[,.]/g,"")},Be=function(e){return typeof e!="object"&&!Number.isNaN(_e(e))},It=function(e){return typeof e=="object"},cn=function(e){return!It(e)&&(en.includes(e)||tn.test(e)||nn.test(e)||rn.test(e))},un=n.memo(n.forwardRef(function(e,s){var a,l,c=e.isNew,N=e.charClassName,y=e.numbersRef,b=e.active,x=e.isChanged,h=e.effectiveDuration,k=e.delay,z=e.duration,V=e.speed,d=e.value,p=e.startValue,j=e.disableStartValue,C=e.dummyList,q=e.hasSequentialDummyList,te=e.hasInfiniteList,F=e.valueClassName,Q=e.numberSlotClassName,R=e.numberClassName,L=e.reverse,D=e.sequentialAnimationMode,K=e.useMonospaceWidth,ne=e.maxNumberWidth,X=e.onFontHeightChange,v=e.slotPeek,T=n.useState(!1),P=T[0],I=T[1],B=n.useState(d),w=B[0],fe=B[1],Z=n.useRef(),ge=n.useRef(d),J=n.useRef(null),xe=n.useState(0),O=xe[0],Re=xe[1],$=n.useState(q?C:wt(C)),Le=$[0],M=$[1],he=n.useState(!1),G=he[0],Te=he[1],pe=O*(C.length+1);nt(function(){Te(!0)},[]),nt(function(){var E,S;Re((S=(E=J.current)===null||E===void 0?void 0:E.getBoundingClientRect().height)!==null&&S!==void 0?S:0)},[G]),n.useEffect(function(){b?requestAnimationFrame(function(){I(b)}):I(b)},[b]),n.useEffect(function(){var E=J.current;if(O&&E&&typeof ResizeObserver<"u"){var S=new ResizeObserver(function(Ue){var ye=Ue[0].contentRect.height;Math.abs(O-ye)>1&&X?.(ye)});return S.observe(E),function(){S.disconnect()}}},[O,X]),n.useMemo(function(){j&&(Z.current=ge.current)},[j]),n.useEffect(function(){P&&(Z.current=ge.current,ge.current=d,setTimeout(function(){return fe(d)},D?0:h*V*z*1e3/C.length+1e3*k))},[P,d,h,k,C.length,D,V,z]),n.useEffect(function(){M(q?C:wt(C))},[C,q]),n.useImperativeHandle(s,function(){return{refreshStyles:function(){var E,S;I(!0),Re((S=(E=J.current)===null||E===void 0?void 0:E.getBoundingClientRect().height)!==null&&S!==void 0?S:0),requestAnimationFrame(function(){I(!1)})}}});var re=function(){return Le.map(function(E,S){return A.createElement("span",{key:S,className:Y(We,R,Pe),"aria-hidden":"true"},E)})},se=L?w:p??w;D&&(se=L?w:(a=p??Z.current)!==null&&a!==void 0?a:w,D&&c&&!L&&(se=""));var ve=L&&p!=null?p:w;D&&(ve=L&&(l=p??Z.current)!==null&&l!==void 0?l:w);var He=n.useMemo(function(){if(G)return K?ne:void 0},[G,ne,K]),g=n.useMemo(function(){if(G)return v?O+2*v:O},[G,O,v]);return A.createElement("span",{className:Y(Xt,N,Ct),style:Ge({display:"inline-block",width:He,height:g},v&&{paddingTop:v,paddingBottom:v})},A.createElement("span",{ref:y,className:Y(Jt,Q,ln),style:Ge({transition:"none",transform:L?"translateY(-".concat(pe,"px)"):"translateY(0px)"},P&&x&&{transform:L?"translateY(0px)":"translateY(-".concat(pe,"px)"),transition:"transform ".concat(h,"s ").concat(k,"s ease-in-out")})},G?A.createElement(A.Fragment,null,v&&A.createElement("div",{className:$t},re()),A.createElement("span",{className:Y(We,R,Pe),"aria-hidden":"true",style:{height:O}},se),re(),A.createElement("span",{className:Y(We,R,F,Pe,on),ref:J},ve),te||v?re():null):A.createElement("span",{className:Y(We,R,Pe),"aria-hidden":"true"},p??w)))})),mn=n.memo(n.forwardRef(function(e,s){var a,l,c,N,y,b,x,h,k,z,V=e.value,d=e.startValue,p=e.startValueOnce,j=p!==void 0&&p,C=e.duration,q=C===void 0?.7:C,te=e.speed,F=te===void 0?1.4:te,Q=e.delay,R=e.dummyCharacters,L=e.dummyCharacterCount,D=L===void 0?6:L,K=e.autoAnimationStart,ne=K===void 0||K,X=e.containerClassName,v=e.charClassName,T=e.separatorClassName,P=e.valueClassName,I=e.numberSlotClassName,B=e.numberClassName,w=e.animateUnchanged,fe=w!==void 0&&w,Z=e.hasInfiniteList,ge=Z!==void 0&&Z,J=e.sequentialAnimationMode,xe=J!==void 0&&J,O=e.useMonospaceWidth,Re=O!==void 0&&O,$=e.direction,Le=e.debounceDelay,M=e.animateOnVisible,he=e.startFromLastDigit,G=he!==void 0&&he,Te=e.onAnimationStart,pe=e.onAnimationEnd,re=e.separatorCharacters,se=e.isSeparatorCharacter,ve=se===void 0?cn:se,He=e.slotPeek,g=function(r,i){var o=n.useState(r),f=o[0],m=o[1];return n.useEffect(function(){if(i===0)return m(r);var _=setTimeout(function(){m(r)},i);return function(){clearTimeout(_)}},[r,i]),f}(V,Le??0),E=n.useMemo(function(){return function(r){return Array.isArray(r)&&It(r[0])}(g)?"":typeof g=="object"?JSON.stringify(g):g.toString()},[g]),S=n.useState(!1),Ue=S[0],ye=S[1],ae=n.useRef(),ie=n.useRef(null),be=n.useRef(null),Me=n.useRef(d),rt=n.useRef([]),Ye=n.useMemo(function(){return typeof M=="boolean"?M:typeof M=="object"||void 0},[M]),st=n.useMemo(function(){return typeof M=="object"?M.rootMargin:void 0},[M]),at=n.useMemo(function(){return typeof M=="object"?M.triggerOnce:void 0},[M]),Ve=n.useRef(!0),ee=!Ye&&ne,le=n.useRef(d==null||ee?g:d),H=n.useRef(d),Ne=n.useRef(0),ze=n.useRef(0),it=n.useState([]),Et=it[0],At=it[1],Qe=n.useRef(),lt=n.useState(0),_t=lt[0],Rt=lt[1],ot=n.useState(),Lt=ot[0],Tt=ot[1],je=n.useRef(!1),Ke=d!=null&&(!j||Ne.current<1),ct=(l=(a=ae.current)===null||a===void 0?void 0:a.dummyCharacterCount)!==null&&l!==void 0?l:D,Xe=(N=(c=ae.current)===null||c===void 0?void 0:c.duration)!==null&&N!==void 0?N:q,Ze=n.useRef({onAnimationStart:Te,onAnimationEnd:pe});Ze.current={onAnimationStart:Te,onAnimationEnd:pe};var Je=n.useRef(!1),qe=n.useCallback(function(){var r=be.current;if(r){var i=bt(0,10).map(function(f){var m=document.createElement("span");m.className=P??"",m.style.position="absolute",m.style.top="0",m.style.left="-9999px",m.style.visibility="hidden",m.textContent=f.toString(),r.appendChild(m);var _=m.getBoundingClientRect().width;return r.removeChild(m),_}),o=Math.max.apply(Math,i);Tt(o)}},[P]);nt(function(){var r;qe(),(r=document.fonts)===null||r===void 0||r.ready.then(function(){qe()})},[]),n.useEffect(function(){At(bt(0,ct*q*F-1).map(function(r){if(!R)return jt(0,10);var i=r>=R.length?jt(0,R.length):r;return R[i]}))},[R,ct,F,q]),le.current!==g&&je.current&&ze.current>0&&(H.current=le.current,le.current=g);var ut=Array.isArray(H.current)?H.current:(b=(y=H.current)===null||y===void 0?void 0:y.toString().split(""))!==null&&b!==void 0?b:[],we=Array.isArray(le.current)?le.current:(h=(x=le.current)===null||x===void 0?void 0:x.toString().split(""))!==null&&h!==void 0?h:[],Mt=Array.isArray(Me.current)?Me.current:(z=(k=Me.current)===null||k===void 0?void 0:k.toString().split(""))!==null&&z!==void 0?z:[],$e=n.useMemo(function(){return Array.isArray(g)?g:g?.toString().split("")},[g]),Se=n.useMemo(function(){return Array.isArray(d)?d:d?.toString().split("")},[d]),zt=ut.length!==we.length,De=[];we.forEach(function(r,i){var o=we.length-i-1,f=Ke?Mt:ut;(we[o]!==f[o]||zt||fe)&&De.push(o)}),G||De.reverse();var qt=n.useMemo(function(){return Q||Math.min(.1,Xe/$e.length)},[Xe,$e.length,Q]),Oe=n.useCallback(function(){var r,i,o;(i=(r=Ze.current).onAnimationEnd)===null||i===void 0||i.call(r),Je.current=!1,(o=be.current)===null||o===void 0||o.removeEventListener("transitionend",Oe)},[]),oe=n.useCallback(function(){var r,i,o;Qe.current&&window.cancelAnimationFrame(Qe.current),Je.current&&Oe(),Je.current=!0,(r=be.current)===null||r===void 0||r.addEventListener("transitionend",Oe),(o=(i=Ze.current).onAnimationStart)===null||o===void 0||o.call(i),ye(!1),Ne.current=ze.current,Ne.current+=1,window.requestAnimationFrame(function(){var f;(f=be.current)===null||f===void 0||f.offsetWidth,Qe.current=requestAnimationFrame(function(){ze.current+=1,ye(!0)})})},[Oe]),Dt=n.useCallback(function(r){var i,o,f=Ke?d:H.current;if(f==null||!Be(f)||!Be(g))return[];var m=f.toString().length,_=g.toString().length,Ce=m<_,Ie=Math.abs(m-_),ue=Number(_e(f.toString())),Ee=Number(_e(g.toString())),me=Number(ue.toString()[Ce?-Ie+r:Ie+r]||0),tt=Number(Ee.toString()[r]||0);if(tt===me)return[];var ht=ue<Ee,pt=ht?Nt((me+1)%10,tt):Nt((tt+1)%10,me);return((o=(i=ae.current)===null||i===void 0?void 0:i.direction)!==null&&o!==void 0?o:$)!=="bottom-up"||ht?pt:pt.reverse()},[Ke,g,d,$]),ke=n.useCallback(function(){rt.current.forEach(function(r){r.refreshStyles()}),qe()},[qe]);n.useEffect(function(){(je.current||H.current!=null)&&(je.current||Me.current==null)&&(je.current||ee)&&oe()},[E,oe,ee]),n.useEffect(function(){ee&&oe()},[ee,oe]),n.useEffect(function(){requestAnimationFrame(function(){je.current=!0})},[]),n.useImperativeHandle(s,function(){return{startAnimation:et,refreshStyles:ke,reload:function(){return Rt(function(r){return r+1})}}});var ce=d==null||ee||Ne.current!==0?$e:Se||[],Ot=(Se?.length||0)-ce.length,mt=function(r){var i,o=n.useRef(r),f=n.useRef(o.current),m=r.join(","),_=(i=o.current)===null||i===void 0?void 0:i.join(",");return n.useEffect(function(){_!==m&&(f.current=_?.split(",")||[],o.current=m.split(","))},[m,_]),{getPrevDependencies:n.useCallback(function(){return f.current},[]),setPrevDependenciesToSameAsCurrent:n.useCallback(function(){f.current=o.current},[])}}(ce),Wt=mt.getPrevDependencies,dt=mt.setPrevDependenciesToSameAsCurrent,ft=ce.length-Wt().length,et=n.useCallback(function(r){d==null||j||(H.current=void 0),ae.current=r,oe(),dt()},[d,j,oe,dt]),Pt=n.useMemo(function(){return function(r,i){var o;return function(){for(var f=[],m=0;m<arguments.length;m++)f[m]=arguments[m];clearTimeout(o),o=setTimeout(function(){r.apply(void 0,f)},i)}}(function(){ke()},0)},[ke]);n.useEffect(function(){if(ie.current&&window.IntersectionObserver){var r=new IntersectionObserver(function(i){i[0].isIntersecting&&ke()});return r.observe(ie.current),function(){return r.disconnect()}}},[ke]),n.useEffect(function(){if(Ye&&ie.current&&window.IntersectionObserver){var r=new IntersectionObserver(function(o){o[0].isIntersecting&&Ve.current&&(et(),Ve.current=!1,at&&(r.disconnect(),i.disconnect()))},{rootMargin:st,threshold:1}),i=new IntersectionObserver(function(o){o[0].isIntersecting||(Ve.current=!0)},{threshold:0});return r.observe(ie.current),i.observe(ie.current),function(){r.disconnect(),i.disconnect()}}},[Ye,st,at,et]);var gt=function(r){return re&&typeof r=="string"?re.includes(r):ve!==null&&ve(r)},Bt=De.filter(function(r){return!gt(ce[r])}),xt=-1;return A.createElement("span",{key:_t,ref:ie,className:Y(X,Kt,sn)},ce.map(function(r,i){var o,f,m=De.includes(i),_=(m?Bt.indexOf(i):0)*qt,Ce=H.current,Ie=d!=null&&!!j&&Ne.current>1,ue=g!=null&&Ce!=null&&Be(g)&&Be(Ce)&&_e(g)<_e(Ce);if(!((o=ae.current)===null||o===void 0)&&o.direction&&(ue=((f=ae.current)===null||f===void 0?void 0:f.direction)==="top-down"),$&&(ue=$==="top-down"),gt(r))return A.createElement("span",{key:we.length-i-1,className:Y(Zt,T,Ct,an)},r);var Ee=xe&&(!ee||ze.current>1);return xt+=1,A.createElement(un,{key:ce.length-i-1,index:i,isNew:ft>0&&i<ft,maxNumberWidth:Lt,numbersRef:be,active:Ue,isChanged:m,charClassName:v,effectiveDuration:Xe,delay:_,value:r,startValue:Ie||Se==null?void 0:Se[i+Ot],disableStartValue:Ie,dummyList:Ee?Dt(xt):Et,hasSequentialDummyList:Ee,hasInfiniteList:ge,valueClassName:P,numberSlotClassName:I,numberClassName:B,reverse:ue,sequentialAnimationMode:xe,useMonospaceWidth:Re,onFontHeightChange:Pt,speed:F,duration:q,slotPeek:He,ref:function(me){me&&rt.current.push(me)}})}))}));function dn(e){const s=new Set;for(;s.size<3;){const a=Math.floor(Math.random()*(e-1+1))+1;s.add("image"+a)}return Array.from(s)}const u={image1:"https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/burger.png",image2:"https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/donut.png",image3:"https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/cola.png",image4:"https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/cabbage.png",image5:"https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/chicken.png",image6:"https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/egg.png"},fn=[t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image1},"dummyImg1"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image2},"dummyImg2"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image3},"dummyImg3"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image4},"dummyImg4"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image5},"dummyImg5"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image6},"dummyImg6")],W={IDLE:"idle",SPINNING:"spinning",SHOWING_RESULT:"showing_result",SHOWING_WIN:"showing_win"};function gn({SLOTS_MAX_BET:e}){const[s,a]=n.useState(0),[l,c]=n.useState(0),[N,y]=n.useState(!1),[b,x]=n.useState(0),[h,k]=n.useState(!1),[z,V]=n.useState([t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image1},"image1"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image2},"image2"),t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u.image3},"image3")]),[d,p]=n.useState(W.IDLE),[j,C]=n.useState(null),[q,te]=n.useState(0),F=St(),{data:Q}=Ut(),{width:R,height:L}=Qt(),D=n.useRef(null);n.useLayoutEffect(()=>{D.current?.refreshStyles()},[R,L]),n.useEffect(()=>{let v=[];const T=(I,B)=>{const w=setTimeout(I,B);return v.push(w),w},P=()=>{v.forEach(clearTimeout),v=[]};switch(d){case W.SPINNING:k(!0),j&&T(()=>{if(j.won){const I="image"+(j.multiplier-1),B=Array(3).fill(t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u[I],alt:""}));V(B)}else{const B=dn(6).map((w,fe)=>t.jsx("img",{className:"slotImage size-28 scale-75 md:size-40 md:scale-[.65]",src:u[w],alt:""},fe));V(B)}},500),T(()=>{k(!1),p(W.SHOWING_RESULT),F.invalidateQueries({queryKey:U.user.getCurrentUserInfo.key()})},3e3);break;case W.SHOWING_RESULT:if(j?.won){const I=q*j.multiplier;N?T(()=>c(I),500):c(I),y(!0),x(j.multiplier),p(W.SHOWING_WIN)}else p(W.IDLE);break;case W.SHOWING_WIN:T(()=>{y(!1),x(0),p(W.IDLE)},3e3);break;case W.IDLE:default:C(null);break}return P},[d,j,q,N,F]);const K=kt(U.casino.gamble.mutationOptions({onSuccess:v=>{a(0),te(s),C(v),p(W.SPINNING),D.current?.startAnimation({duration:3})},onError:v=>{const T=v.message||"Unknown error occurred";console.error(T),de.error(T),p(W.IDLE)}})),ne=()=>{if(s){if(s>e){de.error(`The max bet is ¥${e}!`);return}if(s<5){de.error("The min bet is ¥5!");return}if(s>Q?.cash){de.error("You don't have enough cash to bet that much!");return}y(!1),K.mutate({amount:s})}},X=()=>{a(Q?.cash)};return t.jsxs("div",{children:[t.jsx("p",{className:"text-center font-accent text-3xl xl:mr-24",children:"Slots"}),t.jsxs("div",{className:"relative mx-auto flex flex-col rounded-lg p-3",children:[t.jsxs("div",{className:"md:-translate-x-20 mx-auto flex gap-6 md:h-[162px]",children:[t.jsxs("div",{className:"hidden h-fit w-52 flex-col gap-2 rounded-lg border-2 border-slate-600 bg-slate-200 pb-1 shadow-lg md:flex dark:bg-slate-800",children:[t.jsx("p",{className:"w-full rounded-t-md border-gray-600 border-b bg-gray-200 py-1 text-center font-accent dark:bg-gray-900 dark:text-stroke-md",children:"Multipliers"}),t.jsxs("div",{className:"flex-col gap-2 px-1 md:flex",children:[t.jsxs("div",{className:Ae("flex justify-center rounded-md py-0.5",b===2&&"bg-green-400 shadow-lg"),children:[t.jsxs("div",{className:"flex w-3/4",children:[t.jsx("img",{className:"w-[33.33%]",src:u.image1,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image1,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image1,alt:""})]}),t.jsx("p",{className:"my-auto ml-1 text-green-500 text-stroke-sm",children:"= 2x"})]}),t.jsxs("div",{className:Ae("flex justify-center rounded-md py-0.5",b===3&&"bg-green-400 shadow-lg"),children:[t.jsxs("div",{className:"flex w-3/4",children:[t.jsx("img",{className:"w-[33.33%]",src:u.image2,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image2,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image2,alt:""})]}),t.jsx("p",{className:"my-auto ml-1 text-blue-500 text-stroke-sm",children:" = 3x"})]}),t.jsxs("div",{className:Ae("flex justify-center rounded-md py-0.5",b===4&&"bg-green-400 shadow-lg"),children:[t.jsxs("div",{className:"flex w-3/4",children:[t.jsx("img",{className:"w-[33.33%]",src:u.image3,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image3,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image3,alt:""})]}),t.jsx("p",{className:"my-auto ml-1 text-amber-500 text-stroke-sm",children:" = 4x"})]})]})]}),t.jsx(mn,{ref:D,containerClassName:"border-2 border-slate-600 shadow-lg rounded-xl divide-x-2 dark:divide-slate-600 divide-slate-600 dark:bg-slate-800 bg-slate-200 ",autoAnimationStart:!1,duration:0,dummyCharacterCount:60,value:z,dummyCharacters:fn})]}),t.jsxs("div",{className:"mx-auto mt-5 flex w-fit flex-col justify-center rounded-md border border-slate-600 px-2 py-3 md:px-8 dark:bg-slate-800",children:[t.jsxs("div",{className:"flex flex-row",children:[t.jsxs("div",{className:"flex flex-col",children:[t.jsx("label",{htmlFor:"betAmount",className:"mb-0.5 block w-28 font-body font-semibold text-gray-900 text-sm dark:text-white",children:"BET AMOUNT"}),t.jsxs("div",{className:"relative",children:[t.jsx("input",{id:"betAmount",name:"betAmount",type:"number",className:"mr-5 block h-12 w-32 rounded-md border border-gray-300 bg-gray-50 p-2.5 pl-10 text-center text-xl shadow-lg focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-slate-200 dark:text-stroke-sm dark:focus:border-blue-500 dark:focus:ring-blue-500 dark:placeholder:text-gray-400",placeholder:"0",min:0,max:e,value:s,onChange:v=>a(parseInt(v.target.value))}),t.jsx("div",{className:"-translate-y-1/2 absolute top-1/2 flex h-full w-8 rounded-l-md border bg-slate-800 text-stroke-sm dark:border-gray-600",children:t.jsx("p",{className:"m-auto text-gray-300 text-xl",children:"¥"})})]})]})," ",t.jsx(Fe,{height:"h-10!",rounded:"rounded-lg",className:"mt-6 w-24 font-bold font-display text-xl",type:"secondary",disabled:h,onClick:()=>X(),children:"All In!"})]}),t.jsx(Fe,{height:"h-12!",rounded:"rounded-lg",className:"mx-auto mt-4 w-40 font-bold font-display text-xl",type:"tertiary",disabled:h||!s,onClick:()=>ne(),children:"SPIN!"})]}),t.jsxs("div",{className:"mx-auto mt-4 flex w-1/2 flex-col gap-2 rounded-lg border-2 border-slate-600 bg-slate-200 py-2 shadow-lg md:hidden dark:bg-slate-800",children:[t.jsxs("div",{className:"flex justify-center",children:[t.jsxs("div",{className:"flex w-3/5",children:[t.jsx("img",{className:"w-[33.33%]",src:u.image1,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image1,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image1,alt:""})]}),t.jsx("p",{className:"my-auto ml-1 text-green-500 text-stroke-sm",children:"= 2x"})]}),t.jsxs("div",{className:"flex justify-center",children:[t.jsxs("div",{className:"flex w-3/5",children:[t.jsx("img",{className:"w-[33.33%]",src:u.image2,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image2,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image2,alt:""})]}),t.jsx("p",{className:"my-auto ml-1 text-blue-500 text-stroke-sm",children:" = 3x"})]}),t.jsxs("div",{className:"flex justify-center",children:[t.jsxs("div",{className:"flex w-3/5",children:[t.jsx("img",{className:"w-[33.33%]",src:u.image3,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image3,alt:""}),t.jsx("img",{className:"w-[33.33%]",src:u.image3,alt:""})]}),t.jsx("p",{className:"my-auto ml-1 text-amber-500 text-stroke-sm",children:" = 4x"})]})]}),t.jsxs("p",{className:Ae("z-10 mt-6 text-center text-4xl dark:text-stroke-md",N?"fading-text-visible":"fading-text"),children:["You won ",t.jsxs("span",{className:"w-28 text-indigo-400",children:["¥",l,"!"]})]})]})]})}function pn(){const{SLOTS_MAX_BET:e,CASINO_DISABLED:s,LOTTERY_TICKET_COST:a,LOTTERY_DISABLED:l}=Yt();return s?t.jsx("div",{className:"mt-10 flex flex-col dark:text-slate-200",children:t.jsxs("div",{className:"mx-auto text-center",children:[t.jsx("h2",{className:"text-xl",children:"Casino currently Disabled"}),t.jsx("p",{children:"Please return later."})]})}):t.jsxs("div",{className:"md:-mt-2 mt-2 flex flex-col-reverse gap-4 md:mx-auto md:max-w-6xl md:px-8 md:pb-8 lg:flex-col lg:gap-8 dark:text-white",children:[t.jsx("div",{className:"mx-2 rounded-lg border border-gray-600 bg-slate-900 pt-4 lg:mx-auto lg:pl-24",children:t.jsx(gn,{SLOTS_MAX_BET:e})}),t.jsxs("div",{className:"mx-2 rounded-lg border border-gray-600 bg-slate-900 px-4 py-3 lg:mx-auto lg:w-fit lg:p-5",children:[t.jsx("p",{className:"text-center font-accent text-3xl",children:"Daily Lottery"}),l&&t.jsx("p",{className:"mt-2 text-center text-red-500 text-xl",children:"Currently Disabled"}),t.jsx(Vt,{ticketCost:a,disabled:l})]})]})}export{pn as default};
