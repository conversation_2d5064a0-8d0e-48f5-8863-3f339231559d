import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AppRouterClient } from "@/lib/orpc";

export type BattleAttackResponse = Awaited<ReturnType<AppRouterClient["battle"]["attack"]>>;

export const useStartAttack = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.attack.mutationOptions({
            // Don't invalidate battle status to prevent UI from resetting
            onSuccess: (data) => {
                if (data.battleState.state === "finished") {
                    queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                }
            },
        })
    );
};

export default useStartAttack;
